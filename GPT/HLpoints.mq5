//+------------------------------------------------------------------+
//|                                                     HighLowLines |
//|                       Custom Indicator for MT5                   |
//|  Draws lines for the last N swing highs and swing lows            |
//+------------------------------------------------------------------+
#property indicator_chart_window   // Indicator is drawn in the chart window
#property indicator_buffers 0      // No buffers needed since we are drawing objects

// Input variables
input int N = 4;                  // Number of swing highs and lows to mark
input int LookBackPeriod = 3;      // Number of surrounding candles to check for swings

// Indicator initialization function
int OnInit()
  {
   // Remove all objects created by this indicator from previous runs
   RemoveLines();
   return(INIT_SUCCEEDED);
  }

// Indicator deinitialization function
void OnDeinit(const int reason)
  {
   // Remove all the drawing objects created by this indicator when deinitialized
   RemoveLines();
  }

// Function to remove all high and low lines
void RemoveLines()
  {
   for(int i = 0; i < N; i++)
     {
      ObjectDelete(0, "HighLine_" + IntegerToString(i));  // Correct usage of ObjectDelete
      ObjectDelete(0, "LowLine_" + IntegerToString(i));   // Correct usage of ObjectDelete
     }
  }

// Function to check if a candle is a swing high
bool IsSwingHigh(int index, const double &high[])
  {
   for(int i = 1; i <= LookBackPeriod; i++)
     {
      if(high[index] <= high[index - i] || high[index] <= high[index + i])
         return(false);
     }
   return(true);
  }

// Function to check if a candle is a swing low
bool IsSwingLow(int index, const double &low[])
  {
   for(int i = 1; i <= LookBackPeriod; i++)
     {
      if(low[index] >= low[index - i] || low[index] >= low[index + i])
         return(false);
     }
   return(true);
  }

// Indicator iteration function
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   // Ensure we have enough bars to calculate
   if(rates_total <= LookBackPeriod) return(0);

   // Variables to store swing highs and lows
   int foundHighs = 0;
   int foundLows = 0;

   // Remove previous lines before drawing new ones
   RemoveLines();

   // Loop through the bars and find swing highs and lows
   for(int i = LookBackPeriod; i < rates_total - LookBackPeriod; i++)
     {
      if(foundHighs < N && IsSwingHigh(i, high))
        {
         // Draw or update the horizontal line for the swing high
         string highLineName = "HighLine_" + IntegerToString(foundHighs);
         ObjectCreate(0, highLineName, OBJ_HLINE, 0, 0, high[i]);
         ObjectSetInteger(0, highLineName, OBJPROP_COLOR, clrBlue);
         ObjectSetInteger(0, highLineName, OBJPROP_WIDTH, 2);
         foundHighs++;
        }

      if(foundLows < N && IsSwingLow(i, low))
        {
         // Draw or update the horizontal line for the swing low
         string lowLineName = "LowLine_" + IntegerToString(foundLows);
         ObjectCreate(0, lowLineName, OBJ_HLINE, 0, 0, low[i]);
         ObjectSetInteger(0, lowLineName, OBJPROP_COLOR, clrRed);
         ObjectSetInteger(0, lowLineName, OBJPROP_WIDTH, 2);
         foundLows++;
        }

      // Stop once we've found the required number of highs and lows
      if(foundHighs >= N && foundLows >= N) break;
     }

   return(rates_total);
  }
