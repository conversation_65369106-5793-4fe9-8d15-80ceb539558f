#property indicator_chart_window
#property indicator_buffers 1
#property indicator_plots 1
#property indicator_type1 DRAW_NONE
#property indicator_label1 "_dummy"
#property strict

// Multi-MA Confluence Debugging Indicator (MQL5)
// - Prints MA confluence, RSI, MACD, <PERSON><PERSON>er, ATR and <PERSON><PERSON><PERSON> checks per bar
// - Shows "Entry: BUY/SELL" text instead of opening trades
// - Designed for debugging / educational purposes

// -------------------- Inputs --------------------
input int    Inp_ShowBars                  = 200;   // How many recent bars to display

// Moving Averages
input int    Inp_LWMA_Period               = 50;    // LWMA 50
input int    Inp_EMA8_Period               = 8;     // EMA 8
input int    Inp_EMA16_Period              = 16;    // EMA 16
input int    Inp_EMA10_Period              = 10;    // EMA 10
input int    Inp_EMA20_Period              = 20;    // EMA 20
input int    Inp_EMA50_Period              = 50;    // EMA 50
input int    Inp_EMA75_Period              = 75;    // EMA 75
input int    Inp_EMA100_Period             = 100;   // EMA 100
input int    Inp_EMA200_Period             = 200;   // EMA 200 (current TF)

// HTF filter
input ENUM_TIMEFRAMES Inp_HTF              = PERIOD_H1;  // Higher timeframe for EMA200 filter
input int    Inp_HTF_EMA200_Period         = 200;   // HTF EMA200

// RSI / MACD / BB / ATR
input int    Inp_RSI_Period                = 14;
input int    Inp_MACD_Fast                 = 12;
input int    Inp_MACD_Slow                 = 26;
input int    Inp_MACD_Signal               = 9;
input int    Inp_BB_Period                 = 20;
input double Inp_BB_Deviation              = 2.0;
input int    Inp_ATR_Period                = 14;

// CHOCH / Swings
input int    Inp_Swing_Left                = 2;
input int    Inp_Swing_Right               = 2;
input int    Inp_CHOCH_Lookback           = 30;
input int    Inp_Retest_Lookback          = 10;
input double Inp_Retest_Tolerance_Pips    = 2.0;

// Bollinger tolerance
input int    Inp_BB_Touch_Lookback        = 30;
input double Inp_BB_Middle_Tolerance_Pips = 1.5;

// Visual
input string Inp_Prefix                   = "Dbg_";
input color  Inp_BuyColor                 = clrLime;
input color  Inp_SellColor                = clrTomato;
input int    Inp_FontSize                 = 9;

// ------------------ Internal --------------------
double dummyBuffer[];

// Indicator handles
int hLWMA=INVALID_HANDLE, hEMA8=INVALID_HANDLE, hEMA16=INVALID_HANDLE, hEMA10=INVALID_HANDLE, hEMA20=INVALID_HANDLE;
int hEMA50=INVALID_HANDLE, hEMA75=INVALID_HANDLE, hEMA100=INVALID_HANDLE, hEMA200=INVALID_HANDLE;
int hRSI=INVALID_HANDLE, hMACD=INVALID_HANDLE, hBB=INVALID_HANDLE, hATR=INVALID_HANDLE;
int hHTF_EMA200=INVALID_HANDLE;

// ------------------ Helpers ---------------------
int PointsPerPip()
{
   int d = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   return (d==3 || d==5) ? 10 : 1;
}

double PipToPointsDouble(double pips)
{
   return pips * PointsPerPip() * _Point;
}

bool GetBufVal(int handle, int bufIndex, int shift, double &out)
{
   if(handle==INVALID_HANDLE) return false;
   double tmp[1];
   ArraySetAsSeries(tmp,true);
   int copied = CopyBuffer(handle, bufIndex, shift, 1, tmp);
   if(copied <= 0) return false;
   out = tmp[0];
   return true;
}

// Delete existing debug objects (prefix)
void DeleteDebugObjects()
{
   int total = ObjectsTotal(0);
   for(int i=total-1; i>=0; --i)
   {
      string name = ObjectName(0, i);
      if(StringLen(name) > 0 && StringFind(name, Inp_Prefix) == 0)
         ObjectDelete(0, name);
   }
}

// Check swing high/low
bool IsSwingHighIdx(const int idx, const double &high[], const int rates_total)
{
   if(idx - Inp_Swing_Left < 0) return false;
   if(idx + Inp_Swing_Right >= rates_total) return false;
   double val = high[idx];
   for(int i=1; i<=Inp_Swing_Left; ++i) if(high[idx - i] >= val) return false;
   for(int i=1; i<=Inp_Swing_Right; ++i) if(high[idx + i] >= val) return false;
   return true;
}

bool IsSwingLowIdx(const int idx, const double &low[], const int rates_total)
{
   if(idx - Inp_Swing_Left < 0) return false;
   if(idx + Inp_Swing_Right >= rates_total) return false;
   double val = low[idx];
   for(int i=1; i<=Inp_Swing_Left; ++i) if(low[idx - i] <= val) return false;
   for(int i=1; i<=Inp_Swing_Right; ++i) if(low[idx + i] <= val) return false;
   return true;
}

// ------------------ Lifecycle -------------------
int OnInit()
{
   // Dummy buffer required for a custom indicator
   SetIndexBuffer(0, dummyBuffer, INDICATOR_DATA);
   PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_NONE);
   SetIndexLabel(0, "_dummy");

   // Create indicator handles (current timeframe)
   hLWMA   = iMA(_Symbol, PERIOD_CURRENT, Inp_LWMA_Period, 0, MODE_LWMA, PRICE_CLOSE);
   hEMA8   = iMA(_Symbol, PERIOD_CURRENT, Inp_EMA8_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA16  = iMA(_Symbol, PERIOD_CURRENT, Inp_EMA16_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA10  = iMA(_Symbol, PERIOD_CURRENT, Inp_EMA10_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA20  = iMA(_Symbol, PERIOD_CURRENT, Inp_EMA20_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA50  = iMA(_Symbol, PERIOD_CURRENT, Inp_EMA50_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA75  = iMA(_Symbol, PERIOD_CURRENT, Inp_EMA75_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA100 = iMA(_Symbol, PERIOD_CURRENT, Inp_EMA100_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA200 = iMA(_Symbol, PERIOD_CURRENT, Inp_EMA200_Period, 0, MODE_EMA, PRICE_CLOSE);

   hRSI   = iRSI(_Symbol, PERIOD_CURRENT, Inp_RSI_Period, PRICE_CLOSE);
   hMACD  = iMACD(_Symbol, PERIOD_CURRENT, Inp_MACD_Fast, Inp_MACD_Slow, Inp_MACD_Signal, PRICE_CLOSE);
   hBB    = iBands(_Symbol, PERIOD_CURRENT, Inp_BB_Period, 0, Inp_BB_Deviation, PRICE_CLOSE);
   hATR   = iATR(_Symbol, PERIOD_CURRENT, Inp_ATR_Period);

   // HTF EMA200
   hHTF_EMA200 = iMA(_Symbol, Inp_HTF, Inp_HTF_EMA200_Period, 0, MODE_EMA, PRICE_CLOSE);

   // Validate handles
   int handles[] = {hLWMA,hEMA8,hEMA16,hEMA10,hEMA20,hEMA50,hEMA75,hEMA100,hEMA200,hRSI,hMACD,hBB,hATR,hHTF_EMA200};
   for(int i=0;i<ArraySize(handles);++i)
   {
      if(handles[i] == INVALID_HANDLE)
      {
         PrintFormat("[Dbg] Failed to create indicator handle index=%d", i);
         return(INIT_FAILED);
      }
   }

   Print("[Dbg] Multi-MA Confluence Debugger initialized.");
   return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason)
{
   // Release handles
   if(hLWMA!=INVALID_HANDLE)    IndicatorRelease(hLWMA);
   if(hEMA8!=INVALID_HANDLE)    IndicatorRelease(hEMA8);
   if(hEMA16!=INVALID_HANDLE)   IndicatorRelease(hEMA16);
   if(hEMA10!=INVALID_HANDLE)   IndicatorRelease(hEMA10);
   if(hEMA20!=INVALID_HANDLE)   IndicatorRelease(hEMA20);
   if(hEMA50!=INVALID_HANDLE)   IndicatorRelease(hEMA50);
   if(hEMA75!=INVALID_HANDLE)   IndicatorRelease(hEMA75);
   if(hEMA100!=INVALID_HANDLE)  IndicatorRelease(hEMA100);
   if(hEMA200!=INVALID_HANDLE)  IndicatorRelease(hEMA200);
   if(hRSI!=INVALID_HANDLE)     IndicatorRelease(hRSI);
   if(hMACD!=INVALID_HANDLE)    IndicatorRelease(hMACD);
   if(hBB!=INVALID_HANDLE)      IndicatorRelease(hBB);
   if(hATR!=INVALID_HANDLE)     IndicatorRelease(hATR);
   if(hHTF_EMA200!=INVALID_HANDLE) IndicatorRelease(hHTF_EMA200);

   // Remove drawn objects
   DeleteDebugObjects();
   Print("[Dbg] Deinitialized.");
}

int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total<=0) return 0;

   // limit how many bars we draw
   int barsToShow = MathMin(Inp_ShowBars, rates_total);
   int start = rates_total - barsToShow; // chronological arrays: 0..rates_total-1

   // Clear previous debug objects
   DeleteDebugObjects();

   // Iterate bars and compute checks
   for(int i = start; i < rates_total; ++i)
   {
      int shift = rates_total - 1 - i; // shift relative to latest for CopyBuffer

      // read all MA values (current TF)
      double lwma, ema8, ema16, ema10, ema20, ema50, ema75, ema100, ema200;
      if(!GetBufVal(hLWMA,0,shift,lwma)) continue;
      if(!GetBufVal(hEMA8,0,shift,ema8)) continue;
      if(!GetBufVal(hEMA16,0,shift,ema16)) continue;
      if(!GetBufVal(hEMA10,0,shift,ema10)) continue;
      if(!GetBufVal(hEMA20,0,shift,ema20)) continue;
      if(!GetBufVal(hEMA50,0,shift,ema50)) continue;
      if(!GetBufVal(hEMA75,0,shift,ema75)) continue;
      if(!GetBufVal(hEMA100,0,shift,ema100)) continue;
      if(!GetBufVal(hEMA200,0,shift,ema200)) continue;

      // RSI
      double rsi; if(!GetBufVal(hRSI,0,shift,rsi)) continue;

      // MACD: buffer 0=main,1=signal,2=hist
      double macdMain, macdSignal, macdHist;
      if(!GetBufVal(hMACD,0,shift,macdMain)) continue;
      if(!GetBufVal(hMACD,1,shift,macdSignal)) continue;
      if(!GetBufVal(hMACD,2,shift,macdHist)) continue;

      // MACD previous histogram (for increasing/decreasing check)
      double macdHistPrev=0; bool haveHistPrev = false;
      if(shift+1 <= 1000000) // simple guard
         haveHistPrev = GetBufVal(hMACD,2,shift+1,macdHistPrev);

      // Bollinger Bands: buffers 0=upper,1=middle,2=lower
      double bbUpper, bbMiddle, bbLower;
      if(!GetBufVal(hBB,0,shift,bbUpper)) continue;
      if(!GetBufVal(hBB,1,shift,bbMiddle)) continue;
      if(!GetBufVal(hBB,2,shift,bbLower)) continue;

      // ATR
      double atr; if(!GetBufVal(hATR,0,shift,atr)) continue;

      // HTF EMA200 (map time[i] to HTF bar index)
      double htfEma200Val=0; bool haveHTF=false;
      int idxHTF = iBarShift(_Symbol, Inp_HTF, time[i], false);
      if(idxHTF >= 0) haveHTF = GetBufVal(hHTF_EMA200, 0, idxHTF, htfEma200Val);

      // MA confluence checks (approximate same as EA)
      bool priceAbove = close[i] > lwma && close[i] > ema50 && close[i] > ema75;
      bool shortTermUp = ema8 > ema16;
      bool secondaryUp = ema10 > ema20 && ema20 > ema50 && ema50 > ema75;
      bool maConfluenceBull = priceAbove && shortTermUp && secondaryUp;

      bool priceBelow = close[i] < lwma && close[i] < ema50 && close[i] < ema75;
      bool shortTermDn = ema8 < ema16;
      bool secondaryDn = ema10 < ema20 && ema20 < ema50 && ema50 < ema75;
      bool maConfluenceBear = priceBelow && shortTermDn && secondaryDn;

      // RSI filter
      bool rsiOk = (rsi >= 30.0 && rsi <= 70.0);

      // MACD confirmation
      bool macdBull = (macdMain > macdSignal) && (haveHistPrev ? (macdHist > macdHistPrev) : true);
      bool macdBear = (macdMain < macdSignal) && (haveHistPrev ? (macdHist < macdHistPrev) : true);

      // Bollinger context: prior touch of upper/lower within lookback
      bool touchedUpper = false; bool touchedLower = false;
      int lookFrom = MathMax(start, i - Inp_BB_Touch_Lookback);
      for(int j = i-1; j>=lookFrom; --j)
      {
         int sh = rates_total - 1 - j;
         double ub, mb, lb;
         if(!GetBufVal(hBB,0,sh,ub)) continue;
         if(!GetBufVal(hBB,2,sh,lb)) continue;
         if(high[j] >= ub) { touchedUpper = true; break; }
         if(low[j] <= lb) { touchedLower = true; break; }
      }

      double tolMid = PipToPointsDouble(Inp_BB_Middle_Tolerance_Pips);
      bool nearMid = MathAbs(close[i] - bbMiddle) <= tolMid;

      // CHOCH Break-Retest
      bool chochLong=false, chochShort=false;
      double tolRetest = PipToPointsDouble(Inp_Retest_Tolerance_Pips);

      // --- Long CHOCH ---
      int searchStart = i - 1 - Inp_Swing_Right;
      int searchEnd   = MathMax(Inp_Swing_Left, i - Inp_CHOCH_Lookback);
      if(searchStart >= searchEnd)
      {
         for(int s = searchStart; s >= searchEnd; --s)
         {
            if(IsSwingHighIdx(s, high, rates_total))
            {
               double swingPrice = high[s];
               // find break after s and before i
               int breakIdx = -1;
               for(int k = s+1; k <= i-1; ++k)
               {
                  if(close[k] > swingPrice) { breakIdx = k; break; }
               }
               if(breakIdx >= 0)
               {
                  // look for retest after breakIdx
                  int rEnd = MathMin(i-1, breakIdx + Inp_Retest_Lookback);
                  for(int r = breakIdx+1; r <= rEnd; ++r)
                  {
                     if(low[r] <= swingPrice + tolRetest && close[r] > swingPrice)
                     {
                        chochLong = true; break;
                     }
                  }
               }
               if(chochLong) break;
            }
         }
      }

      // --- Short CHOCH ---
      searchStart = i - 1 - Inp_Swing_Right;
      searchEnd   = MathMax(Inp_Swing_Left, i - Inp_CHOCH_Lookback);
      if(searchStart >= searchEnd)
      {
         for(int s = searchStart; s >= searchEnd; --s)
         {
            if(IsSwingLowIdx(s, low, rates_total))
            {
               double swingPrice = low[s];
               int breakIdx = -1;
               for(int k = s+1; k <= i-1; ++k)
               {
                  if(close[k] < swingPrice) { breakIdx = k; break; }
               }
               if(breakIdx >= 0)
               {
                  int rEnd = MathMin(i-1, breakIdx + Inp_Retest_Lookback);
                  for(int r = breakIdx+1; r <= rEnd; ++r)
                  {
                     if(high[r] >= swingPrice - tolRetest && close[r] < swingPrice)
                     {
                        chochShort = true; break;
                     }
                  }
               }
               if(chochShort) break;
            }
         }
      }

      // HTF trend
      bool htfBull = false, htfBear = false;
      if(haveHTF)
      {
         double closeHTF = 0.0;
         // get HTF close for corresponding HTF bar
         int idxHTF_local = iBarShift(_Symbol, Inp_HTF, time[i], false);
         if(idxHTF_local >= 0)
         {
            // iClose with HTF
            closeHTF = iClose(_Symbol, Inp_HTF, idxHTF_local);
            if(closeHTF > htfEma200Val) htfBull = true;
            if(closeHTF < htfEma200Val) htfBear = true;
         }
      }

      // Final entry conditions (mirrors EA logic)
      bool longOk = htfBull && maConfluenceBull && rsiOk && macdBull && touchedUpper && nearMid && chochLong;
      bool shortOk = htfBear && maConfluenceBear && rsiOk && macdBear && touchedLower && nearMid && chochShort;

      // Build debug text
      string text = "T:" + TimeToString(time[i], TIME_DATE|TIME_MINUTES) + "
";
      text += StringFormat("Close=%.5f LWMA=%.5f EMA8=%.5f EMA16=%.5f EMA50=%.5f EMA75=%.5f
", close[i], lwma, ema8, ema16, ema50, ema75);
      text += StringFormat("RSI=%.2f MACD=%.5f SIG=%.5f HIST=%.5f (prev=%.5f)
", rsi, macdMain, macdSignal, macdHist, (haveHistPrev?macdHistPrev:0.0));
      text += StringFormat("BB:Up=%.5f Mid=%.5f Low=%.5f nearMid=%s touchedUp=%s touchedLow=%s
", bbUpper, bbMiddle, bbLower, (nearMid?"Y":"N"), (touchedUpper?"Y":"N"),(touchedLower?"Y":"N"));
      text += StringFormat("ATR=%.5f HTF_EMA200=%.5f htfBull=%s htfBear=%s
", atr, htfEma200Val, (htfBull?"Y":"N"),(htfBear?"Y":"N"));
      text += StringFormat("MAconf(Bull=%s Bear=%s) CHOCH(L=%s S=%s)
", (maConfluenceBull?"Y":"N"),(maConfluenceBear?"Y":"N"),(chochLong?"Y":"N"),(chochShort?"Y":"N"));

      if(longOk) text += "*** ENTRY: BUY ***";
      else if(shortOk) text += "*** ENTRY: SELL ***";
      else text += "Entry: -";

      // Draw text object at price slightly above high for visibility
      string objName = Inp_Prefix + IntegerToString((int)time[i]);
      // Remove if already exists (safety)
      if(ObjectFind(0, objName) >= 0) ObjectDelete(0, objName);
      // Create anchored text object
      bool created = ObjectCreate(0, objName, OBJ_TEXT, 0, time[i], high[i] + (10 * _Point * PointsPerPip()));
      if(created)
      {
         ObjectSetString(0, objName, OBJPROP_TEXT, text);
         ObjectSetInteger(0, objName, OBJPROP_COLOR, (longOk?Inp_BuyColor:(shortOk?Inp_SellColor:clrWhite)));
         ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, Inp_FontSize);
         ObjectSetInteger(0, objName, OBJPROP_CORNER, 0);
         ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
         ObjectSetInteger(0, objName, OBJPROP_SELECTABLE, false);
      }
   }

   return(rates_total);
}
