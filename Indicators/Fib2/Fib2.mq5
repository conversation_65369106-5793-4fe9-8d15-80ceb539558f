//+------------------------------------------------------------------+
//|                                                         Fib2.mq5 |
//|                                  Copyright 2022, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
//--- indicator buffers mapping
   
//---
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    // Calculate the Fibonacci levels for the specified price range
    double fib_levels[];
    ArrayResize(fib_levels, end_price - start_price);
    for (int n = start_price; n < end_price; n++)
        fib_levels[n] = fibonacci(n);

    // Use ChartPaint to draw the Fibonacci levels on the chart
    for (int n = start_price; n < end_price; n++)
    {
        ChartPaint(0, fib_levels[n], n, fib_levels[n],
                   RGB(255, 0, 0), RGB(255, 0, 0),
                   STYLE_SOLID, 2);
    }

    // Return the number of bars the indicator has been calculated for
    return(rates_total);
}
//+------------------------------------------------------------------+
