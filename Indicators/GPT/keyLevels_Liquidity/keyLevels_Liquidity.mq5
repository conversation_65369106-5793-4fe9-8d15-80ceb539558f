//+------------------------------------------------------------------+
//|                                                      KeyLevels.mq5|
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                       https://www.mql5.com       |
//+------------------------------------------------------------------+
#property strict

// Input parameters for look-back periods and styles
input int LookBackPeriod = 50; // Look-back period for calculating key levels
input color SupportColor_ = clrGreen; // Color for support levels
input color ResistanceColor_ = clrCoral; // Color for resistance levels
input int LineWidth = 1; // Line width for levels
input ENUM_LINE_STYLE LineStyle = STYLE_DASH; // Line style for levels
input bool EnableAlerts = false; // Enable alerts when levels are crossed

datetime lastCandleTime;

uchar testopacity=100;//Opacity Varies between 0 and 255 . 0 means 100% transparent , 255 means 0% transparent
uint SupportColorFill=ColorToARGB(SupportColor_,testopacity);
uint ResistanceColorFill=ColorToARGB(ResistanceColor_,testopacity);

//+------------------------------------------------------------------+MT5|CustomIndicator!128
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   //--- Initialize the last candle time
   lastCandleTime = 0;

   //--- Draw initial levels and zones
   DrawAll();

   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   //--- Remove all objects created by this indicator
   RemoveAllObjects();
  }
//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   // This function is required for the indicator to run
   return(rates_total);
  }
//+------------------------------------------------------------------+
//| Custom indicator tick function                                   |
//+------------------------------------------------------------------+
void OnTick()
  {
   datetime currentCandleTime = iTime(NULL, 0, 0);
   
   // Check if we are on a new candle
   if(currentCandleTime != lastCandleTime)
     {
      // Update the drawings on each new candle close
      DrawAll();
      
      // Update the last candle time
      lastCandleTime = currentCandleTime;
     }
  }
//+------------------------------------------------------------------+
//| Draw all levels and zones                                        |
//+------------------------------------------------------------------+
void DrawAll()
  {
   RemoveAllObjects();

   // Calculate key levels and liquidity zones
   double support1, support2, resistance1, resistance2;
   CalculateKeyLevels(LookBackPeriod, support1, support2, resistance1, resistance2);

   // Draw Support and Resistance Levels
   DrawLevels(support1, SupportColor_, "S1");
   DrawLevels(support2, SupportColor_, "S2");
   DrawLevels(resistance1, ResistanceColor_, "R1");
   DrawLevels(resistance2, ResistanceColor_, "R2");

   // Draw Liquidity Zones
   DrawLiquidityZone(support1, SupportColorFill, "LZ 1");
   DrawLiquidityZone(support2, SupportColorFill, "LZ 2");
   DrawLiquidityZone(resistance1, ResistanceColorFill, "LZ 3");
   DrawLiquidityZone(resistance2, ResistanceColorFill, "LZ 4");

   // Print Market Direction
   PrintMarketDirection();

   // Check for alerts
   CheckAlerts(support1, support2, resistance1, resistance2);
  }
//+------------------------------------------------------------------+
//| Calculate Key Levels Function                                    |
//+------------------------------------------------------------------+
void CalculateKeyLevels(int lookBack, double &support1, double &support2, double &resistance1, double &resistance2)
  {
   double highArray[], lowArray[];

   // Copy high and low prices over the look-back period
   CopyHigh(NULL, 0, 0, lookBack, highArray);
   CopyLow(NULL, 0, 0, lookBack, lowArray);

   // Initialize levels
   support1 = support2 = DBL_MAX;
   resistance1 = resistance2 = -DBL_MAX;

   // Find highest and lowest prices in the look-back period
   for(int i = 0; i < ArraySize(highArray); i++)
     {
      if(highArray[i] > resistance1)
        {
         resistance2 = resistance1;
         resistance1 = highArray[i];
        }
      else if(highArray[i] > resistance2)
        {
         resistance2 = highArray[i];
        }

      if(lowArray[i] < support1)
        {
         support2 = support1;
         support1 = lowArray[i];
        }
      else if(lowArray[i] < support2)
        {
         support2 = lowArray[i];
        }
     }
  }
//+------------------------------------------------------------------+
//| Draw Levels Function                                             |
//+------------------------------------------------------------------+
void DrawLevels(double level, color lineColor, string description)
  {
   // Create the level line
   string name = description;
   if(ObjectFind(0, name) == -1)
     {
      ObjectCreate(0, name, OBJ_HLINE, 0, 0, level);
      ObjectSetInteger(0, name, OBJPROP_COLOR, lineColor);
      ObjectSetInteger(0, name, OBJPROP_WIDTH, LineWidth);
      ObjectSetInteger(0, name, OBJPROP_STYLE, LineStyle);
     }
   
   // Create the label for the level
   string labelName = name + "_Label";
   if(ObjectFind(0, labelName) == -1)
     {
      ObjectCreate(0, labelName, OBJ_TEXT, 0, TimeCurrent(), level);
      ObjectSetInteger(0, labelName, OBJPROP_COLOR, lineColor);
      ObjectSetString(0, labelName, OBJPROP_TEXT, " ");
      ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT);
     }
  }
//+------------------------------------------------------------------+
//| Draw Liquidity Zone Function                                     |
//+------------------------------------------------------------------+
void DrawLiquidityZone(double level, uint fillColor, string description)
  {
   // Create the liquidity zone rectangle
   string name = "Zone_" + DoubleToString(level);
   double offset = 0.0005; // Adjust this value to increase/decrease the liquidity zone width
   if(ObjectFind(0, name) == -1)
     {
      datetime time1 = TimeCurrent() - PeriodSeconds() * 50; // Start time of the rectangle
      datetime time2 = TimeCurrent() + PeriodSeconds() * 50; // End time of the rectangle
      ObjectCreate(0, name, OBJ_RECTANGLE, 0, time1, level - offset, time2, level + offset);
      ObjectSetInteger(0, name, OBJPROP_COLOR, clrHoneydew);
      ObjectSetInteger(0, name, OBJPROP_STYLE, STYLE_SOLID);
      ObjectSetInteger(0, name, OBJPROP_WIDTH, 1);
      ObjectSetInteger(0, name, OBJPROP_BACK, true);
      ObjectSetInteger(0, name, OBJPROP_FILL, true);
      ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
      ObjectSetInteger(0, name, OBJPROP_ZORDER, 0);
     }
   
   // Create the label for the zone
   string labelName = name + "_Label";
   if(ObjectFind(0, labelName) == -1)
     {
      ObjectCreate(0, labelName, OBJ_TEXT, 0, TimeCurrent(), level);
      ObjectSetInteger(0, labelName, OBJPROP_COLOR, fillColor);
      ObjectSetString(0, labelName, OBJPROP_TEXT, " ");
      ObjectSetInteger(0, labelName, OBJPROP_ANCHOR, ANCHOR_LEFT);
     }
  }
//+------------------------------------------------------------------+
//| Print Market Direction Function                                  |
//+------------------------------------------------------------------+
void PrintMarketDirection()
  {
  
  string direction5M = GetMarketDirection(PERIOD_M5);
   string direction15M = GetMarketDirection(PERIOD_M15);
   string direction1H = GetMarketDirection(PERIOD_H1);
   string direction4H = GetMarketDirection(PERIOD_H4);

   string text = "(1H): " + direction1H + " --- (4H): " + direction4H ;
   string name = "MarketDirectionLabel";

   if(ObjectFind(0, name) == -1)
     {
      ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, name, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, name, OBJPROP_YDISTANCE, 45);
      ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
      ObjectSetInteger(0, name, OBJPROP_COLOR, clrGreen);
      ObjectSetString(0, name, OBJPROP_TEXT, text);
     }
   else
     {
      ObjectSetString(0, name, OBJPROP_TEXT, text);
     }
     
     string text2 = "(15M): " + direction15M + " --- (5M): " + direction5M ;
   string name2 = "MarketDirectionLabel2";

   if(ObjectFind(0, name2) == -1)
     {
      ObjectCreate(0, name2, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(0, name2, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, name2, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, name2, OBJPROP_YDISTANCE, 65);
      ObjectSetInteger(0, name2, OBJPROP_FONTSIZE, 8);
      ObjectSetInteger(0, name2, OBJPROP_COLOR, clrGreen);
      ObjectSetString(0, name2, OBJPROP_TEXT, text2);
     }
   else
     {
      ObjectSetString(0, name2, OBJPROP_TEXT, text2);
     }
  }
//+------------------------------------------------------------------+
//| Get Market Direction Function                                    |
//+------------------------------------------------------------------+
string GetMarketDirection(ENUM_TIMEFRAMES timeframe)
  {
   int bars = iBars(NULL, timeframe);
   if(bars < 50)
      return "Data not enough";

   int startPos = bars - 50; // Look at the last 50 bars
   double sumClose = 0;

   for(int i = startPos; i < bars; i++)
     {
      sumClose += iClose(NULL, timeframe, i);
     }

   double avgClose = sumClose / 50;
   double currentClose = iClose(NULL, timeframe, 0);

   if(currentClose > avgClose)
     {
      return "Up";
     }
   else if(currentClose < avgClose)
     {
      return "Down";
     }
   else
     {
      return "Sideways";
     }
  }
//+------------------------------------------------------------------+
//| Check Alerts Function                                            |
//+------------------------------------------------------------------+
void CheckAlerts(double support1, double support2, double resistance1, double resistance2)
  {
//   double currentPrice = iClose(NULL, 0, 0);
//
//   if(EnableAlerts)
//     {MT5|CustomIndicator!127
//      if(currentPrice <= support1)
//         Alert("Price has crossed below Support Level 1!");
//      else if(currentPrice <= support2)
//         Alert("Price has crossed below Support Level 2!");
//      else if(currentPrice >= resistance1)
//         Alert("Price has crossed above Resistance Level 1!");
//      else if(currentPrice >= resistance2)
//         Alert("Price has crossed above Resistance Level 2!");
//     }
  }
//+------------------------------------------------------------------+
//| Remove All Objects Function                                      |
//+------------------------------------------------------------------+
void RemoveAllObjects()
  {
   ObjectsDeleteAll(0, 0, -1);
  }
//+------------------------------------------------------------------+
