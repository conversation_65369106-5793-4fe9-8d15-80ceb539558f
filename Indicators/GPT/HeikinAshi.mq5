//+------------------------------------------------------------------+
//|                                                  Heiken_Ashi.mq5 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                       https://www.metaquotes.net |
//+------------------------------------------------------------------+
#property strict

#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots 2
#property indicator_type1 DRAW_CANDLES
#property indicator_type2 DRAW_CANDLES
#property indicator_color1 clrBlue
#property indicator_color2 clrRed

//--- input parameters for colors
input color BullColor = clrBlue;
input color BearColor = clrRed;

//--- indicator buffers
double ExtOpenBuffer[];
double ExtHighBuffer[];
double ExtLowBuffer[];
double ExtCloseBuffer[];

//--- additional buffer for candle colors
double ExtColorBuffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
  {
   //--- indicator buffers mapping
   SetIndexBuffer(0, ExtOpenBuffer);
   SetIndexBuffer(1, ExtHighBuffer);
   SetIndexBuffer(2, ExtLowBuffer);
   SetIndexBuffer(3, ExtCloseBuffer);
   SetIndexBuffer(4, ExtColorBuffer);

   //--- set the indicator short name
   IndicatorSetString(INDICATOR_SHORTNAME, "Heiken Ashi");

   //--- set the labels for DataWindow
   PlotIndexSetString(0, PLOT_LABEL, "HA_Candles");

   //--- set properties for the indicator
   PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_CANDLES);

   //--- indicator colors
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, 0, BullColor);
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, 1, BearColor);

   //--- ensure the chart redraws properly
   ChartRedraw();

   return(INIT_SUCCEEDED);
  }

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   int limit;
   if(prev_calculated == 0)
      limit = 0;
   else
      limit = prev_calculated - 1;

   for(int i = limit; i < rates_total; i++)
     {
      // Calculate Heiken Ashi Open, High, Low, Close
      if(i == 0)
         ExtOpenBuffer[i] = (open[i] + close[i]) / 2;
      else
         ExtOpenBuffer[i] = (ExtOpenBuffer[i-1] + ExtCloseBuffer[i-1]) / 2;

      ExtCloseBuffer[i] = (open[i] + high[i] + low[i] + close[i]) / 4;

      ExtHighBuffer[i] = MathMax(high[i], MathMax(ExtOpenBuffer[i], ExtCloseBuffer[i]));
      ExtLowBuffer[i] = MathMin(low[i], MathMin(ExtOpenBuffer[i], ExtCloseBuffer[i]));

      // Set colors based on whether the close price is higher or lower than the open price
      if (ExtCloseBuffer[i] > ExtOpenBuffer[i])
         ExtColorBuffer[i] = BullColor;
      else
         ExtColorBuffer[i] = BearColor;
     }

   // Update colors
   for (int i = 0; i < rates_total; i++)
   {
      PlotIndexSetInteger(0, PLOT_LINE_COLOR, i, ExtColorBuffer[i]);
   }

   return(rates_total);
  }
//+------------------------------------------------------------------+
