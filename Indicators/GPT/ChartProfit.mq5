//+------------------------------------------------------------------+
//|                                             OpenPositionsIndicator.mq5|
//|                        Generated by MetaEditor 5                 |
//|                                                                  |
//+------------------------------------------------------------------+
#property indicator_chart_window
#property indicator_buffers 0

#include <Trade\Trade.mqh>

input color BoxColor = clrLightGray;
input color TextColor = clrBlack;
input bool CurrentSymbolOnly = true; // apply on current symbol only

int OnInit()
  {
   ChartSetInteger(0, CHART_FOREGROUND, true); // Ensure the indicator is drawn in the foreground
   EventSetTimer(1); // Set a timer to update the indicator every second
   return(INIT_SUCCEEDED);
  }

void OnDeinit(const int reason)
  {
   EventKillTimer(); // Kill the timer when the indicator is removed
   Comment(""); // Clear the comment
   // Remove the details box
   int chart_id = ChartID();
   string obj_name = "details_box";
   ObjectDelete(chart_id, obj_name);
  }
  

void OnTimer()
  {
   DrawInfoBox();
  }

int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   DrawInfoBox();
   return(rates_total);
  }

void DrawInfoBox()
  {
   double x = 10;
   double y = 20;
   double width = 200;
   double height = 60;

   int total_positions = 0;
   int long_positions = 0;
   int short_positions = 0;
   double total_profit = 0;

   for (int i = 0; i < PositionsTotal(); i++)
     {
      ulong ticket = PositionGetTicket(i);
      if (PositionSelectByTicket(ticket))
        {
        if(CurrentSymbolOnly){
        
         if (PositionGetString(POSITION_SYMBOL) == Symbol())
           {
            total_positions++;
            double profit = PositionGetDouble(POSITION_PROFIT);
            total_profit += profit;

            if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
               long_positions++;
            else if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
               short_positions++;
           }
           }else{
             total_positions++;
            double profit = PositionGetDouble(POSITION_PROFIT);
            total_profit += profit;

            if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
               long_positions++;
            else if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL)
               short_positions++;
           }
           
        }
     }

   string text = StringFormat("Long Positions: %d\nShort Positions: %d\nTotal Profit: %.2f", 
                               long_positions, short_positions, total_profit);

   Comment(text);

   // Draw the details box
   int chart_id = ChartID();
   string obj_name = "details_box";
   if (!ObjectFind(chart_id, obj_name))
     {
      ObjectCreate(chart_id, obj_name, OBJ_RECTANGLE_LABEL, 0, 0, 0);
      ObjectSetInteger(chart_id, obj_name, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(chart_id, obj_name, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(chart_id, obj_name, OBJPROP_YDISTANCE, 10);
      ObjectSetInteger(chart_id, obj_name, OBJPROP_COLOR, BoxColor);
      ObjectSetInteger(chart_id, obj_name, OBJPROP_STYLE, STYLE_SOLID);
      ObjectSetInteger(chart_id, obj_name, OBJPROP_WIDTH, 1);
      ObjectSetInteger(chart_id, obj_name, OBJPROP_BACK, true);
     }

   ObjectSetString(chart_id, obj_name, OBJPROP_TEXT, text);
   ObjectSetInteger(chart_id, obj_name, OBJPROP_FONTSIZE, 12);
   ObjectSetInteger(chart_id, obj_name, OBJPROP_COLOR, TextColor);
   ObjectSetInteger(chart_id, obj_name, OBJPROP_ALIGN, ALIGN_LEFT);
   ObjectSetInteger(chart_id, obj_name, OBJPROP_XSIZE, width);
   ObjectSetInteger(chart_id, obj_name, OBJPROP_YSIZE, height);
  }
