//+------------------------------------------------------------------+
//|                                                     HighLowLines |
//|                       Custom Indicator for MT5                   |
//|  Draws lines for the last N swing highs and swing lows            |
//+------------------------------------------------------------------+
#property indicator_chart_window   // Indicator is drawn in the chart window
#property indicator_buffers 1      // Dummy buffer to satisfy plot requirement

double emptyBuffer[];              // Dummy buffer (not used for drawing)

// Input variables
input int N = 4;                  // Number of swing highs and lows to mark
input int LookBackPeriod = 150;      // Number of surrounding candles to check for swings

// Indicator initialization function
int OnInit()
  {
   // Remove any old lines from previous runs
   RemoveLines();
   
   // Start drawing lines on initialization
   return(INIT_SUCCEEDED);
  }

// Indicator deinitialization function
void OnDeinit(const int reason)
  {
   // Remove all the drawing objects created by this indicator when deinitialized
   RemoveLines();
  }

// Function to remove all high and low lines
void RemoveLines()
  {
   for(int i = 0; i < N; i++)
     {
      ObjectDelete(0, "HighLine_" + IntegerToString(i));  // Correct usage of ObjectDelete
      ObjectDelete(0, "LowLine_" + IntegerToString(i));   // Correct usage of ObjectDelete
     }
  }

// Function to check if a candle is a swing high
bool IsSwingHigh(int index, const double &high[])
  {
   for(int i = 1; i <= LookBackPeriod; i++)
     {
      if(high[index] <= high[index - i] || high[index] <= high[index + i])
         return(false);
     }
   return(true);
  }

// Function to check if a candle is a swing low
bool IsSwingLow(int index, const double &low[])
  {
   for(int i = 1; i <= LookBackPeriod; i++)
     {
      if(low[index] >= low[index - i] || low[index] >= low[index + i])
         return(false);
     }
   return(true);
  }

// Function to draw swing highs and lows
void DrawSwingHighLowLines(const double &high[], const double &low[], const datetime &time[])
  {
   int rates_total = Bars(_Symbol,PERIOD_CURRENT);  // Get the total number of bars

   // Variables to store swing highs and lows
   int foundHighs = 0;
   int foundLows = 0;

   // Loop through the bars and find swing highs and lows
   for(int i = LookBackPeriod; i < rates_total - LookBackPeriod; i++)
     {
      // Check for swing highs
      if(foundHighs < N && IsSwingHigh(i, high))  // Use 'high[]' array passed in OnCalculate
        {
         // Draw or update the horizontal line for the swing high
         string highLineName = "HighLine_" + IntegerToString(foundHighs);
         if(!ObjectFind(0, highLineName))
         {
            ObjectCreate(0, highLineName, OBJ_HLINE, 0, time[i], high[i]);  // Use 'time[]' and 'high[]'
            ObjectSetInteger(0, highLineName, OBJPROP_COLOR, clrBlue);
            ObjectSetInteger(0, highLineName, OBJPROP_WIDTH, 2);
            foundHighs++;
         }
        }

      // Check for swing lows
      if(foundLows < N && IsSwingLow(i, low))  // Use 'low[]' array passed in OnCalculate
        {
         // Draw or update the horizontal line for the swing low
         string lowLineName = "LowLine_" + IntegerToString(foundLows);
         if(!ObjectFind(0, lowLineName))
         {
            ObjectCreate(0, lowLineName, OBJ_HLINE, 0, time[i], low[i]);  // Use 'time[]' and 'low[]'
            ObjectSetInteger(0, lowLineName, OBJPROP_COLOR, clrRed);
            ObjectSetInteger(0, lowLineName, OBJPROP_WIDTH, 2);
            foundLows++;
         }
        }

      // Stop once we've found the required number of highs and lows
      if(foundHighs >= N && foundLows >= N) break;
     }
  }

// Indicator iteration function
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],  // Time array is passed here
                const double &open[],
                const double &high[],    // Correct array reference
                const double &low[],     // Correct array reference
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
  {
   // Ensure we have enough bars to calculate
   if(rates_total <= LookBackPeriod) return(0);

   // Only calculate from the last calculated bar
   int start = prev_calculated > 0 ? prev_calculated : LookBackPeriod;

   // Remove previous lines before drawing new ones
   RemoveLines();

   // Draw lines again during recalculation
   DrawSwingHighLowLines(high, low, time);

   return(rates_total);
  }
