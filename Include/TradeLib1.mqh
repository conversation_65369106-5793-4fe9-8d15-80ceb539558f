//+------------------------------------------------------------------+
//|                                                    TradeLib1.mqh |
//|                                  Copyright 2022, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+


double get_ma_val(ENUM_TIMEFRAMES timeframe, ENUM_MA_METHOD ma_method, int ma_period) {
   
   double emaBuffer[];
   ArraySetAsSeries(emaBuffer,true);
   
  int ma_value = iMA(Symbol(), timeframe, ma_period, 1, ma_method, PRICE_CLOSE);   
   
   int ema_copy = CopyBuffer(ma_value,0,0,1,emaBuffer);
   
   return emaBuffer[0];
}


double Check_moving_avg_close(ENUM_TIMEFRAMES timeframe, ENUM_MA_METHOD ma_method, ENUM_APPLIED_PRICE applied_price, int ma_period) {
   double maBuffer[];
   ArraySetAsSeries(maBuffer,true);
   
  int ma_value = iMA(Symbol(), timeframe, ma_period,2, ma_method, applied_price);
  int ma_copy = CopyBuffer(ma_value,0,0,1,maBuffer);
  
  double close_price = iClose(Symbol(), timeframe, 0);

  if (close_price > maBuffer[0]) {
    return 1; // Closed above MA
  } else if (close_price < maBuffer[0]) {
    return 2; // Closed below MA
  } else {
    return 0; // Closed at MA (unlikely but possible)
  }
}

int Check_bb_mid_position(ENUM_TIMEFRAMES timeframe, int bb_period, double bb_deviation) {
  double bb_middle = iBands(Symbol(), timeframe, bb_period, 0, bb_deviation, PRICE_CLOSE); // Corrected iBands
  double close_price = iClose(Symbol(), timeframe, 0);

  if (close_price > bb_middle) {
    return 1; // Closed above BB Middle
  } else if (close_price < bb_middle) {
    return 2; // Closed below BB Middle
  } else {
      return 0;
  }
}

int Check_bb_bands_toch(ENUM_TIMEFRAMES timeframe, int bb_period, double bb_deviation) {
  double bb_upper = iBands(Symbol(), timeframe, bb_period, 0, bb_deviation, PRICE_CLOSE); // Corrected iBands
  double bb_lower = iBands(Symbol(), timeframe, bb_period, 0, bb_deviation, PRICE_CLOSE); // Corrected iBands
  double high_price = iHigh(Symbol(), timeframe, 0);
  double low_price = iLow(Symbol(), timeframe, 0);

  if (high_price >= bb_upper) {
    return 1; // Touched/Breached Upper Band
  } else if (low_price <= bb_lower) {
    return 2; // Touched/Breached Lower Band
  } else {
    return 0;
  }
}

int Check_rsi_levl(ENUM_TIMEFRAMES timeframe, double rsi_level) {
  double rsi_value = iRSI(Symbol(), timeframe, 14, PRICE_CLOSE); // Corrected iRSI
  if (rsi_value > rsi_level) {
    return 1; // RSI above level
  } else if (rsi_value < rsi_level) {
    return 2; // RSI below level
  } else {
    return 0;
  }
}

double Get_last_n_candlers_hl(ENUM_TIMEFRAMES timeframe, int n_candles, bool highest) {
    double extreme_price = 0;
    if (highest)
    {
        extreme_price = iHigh(Symbol(), timeframe, 0);
        for (int i = 1; i < n_candles; i++)
        {
            if(iHigh(Symbol(), timeframe, i) > extreme_price)
                extreme_price = iHigh(Symbol(), timeframe, i);
        }
    }
    else
    {
        extreme_price = iLow(Symbol(), timeframe, 0);
        for (int i = 1; i < n_candles; i++)
        {
             if(iLow(Symbol(), timeframe, i) < extreme_price)
                extreme_price = iLow(Symbol(), timeframe, i);
        }
    }
    return extreme_price;
}



//+------------------------------------------------------------------+
//| Function to Get RSI Value                                        |
//+------------------------------------------------------------------+
double GetRSI(string symbol, ENUM_TIMEFRAMES timeframe, int period, int applied_price, int shift)
{
   // Ensure the RSI handle is valid
   int handle = iRSI(symbol, timeframe, period, applied_price);
   if (handle == INVALID_HANDLE)
   {
      Print("Error creating RSI handle: ", GetLastError());
      return -1; // Return an invalid value
   }

   // Array to store RSI values
   double rsiBuffer[];
   if (CopyBuffer(handle, 0, shift, 1, rsiBuffer) <= 0)
   {
      Print("Error copying RSI data: ", GetLastError());
      IndicatorRelease(handle); // Clean up
      return -1;
   }

   // Release the RSI handle to free resources
   IndicatorRelease(handle);

   // Return the RSI value for the specified shift
   return rsiBuffer[0];
}


