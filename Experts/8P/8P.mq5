#include <Trade/Trade.mqh>

//+------------------------------------------------------------------+
//|                                                           8P.mq5 |
//|                                  Copyright 2022, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+


input double Lots =0.1;

int maHandle_SMA_8;
int barsTotal;
int maDirection;

CTrade trade;

int OnInit(){

   maHandle_SMA_8= iMA(_Symbol,_Period,8,0,MODE_SMA,PRICE_CLOSE);
   barsTotal = iBars(_Symbol,_Period);
           
   
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
//---
   
  }
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick(){


      double Ask=SymbolInfoDouble(_Symbol,SYMBOL_ASK);
      double Bid=SymbolInfoDouble(_Symbol,SYMBOL_BID);

    
      //ObjectCreate(0,"lable11",OBJ_LABEL,0,0,0);
      //ObjectSetString(0,"lable11",OBJPROP_FONT,"Arial");
      //ObjectSetInteger(0,"lable11",OBJPROP_FONTSIZE,12);
      //ObjectSetString(0,"lable11",OBJPROP_TEXT,"ASK : "+ opne + " -- Bid: "+ cloe);
      //ObjectSetInteger(0,"lable11",OBJPROP_XDISTANCE, 20);
      //ObjectSetInteger(0,"lable11",OBJPROP_YDISTANCE, 20);
      
     
      
      
      
   int bars= iBars(_Symbol,_Period);
   if(barsTotal < bars){
      barsTotal = bars;
      
      //double Open =NormalizeDouble(iOpen(NULL,_Period,1),_Digits);
      //double Close = NormalizeDouble(iClose(NULL,_Period,1),_Digits);
      
//      Comment("OPE: "+Open+" -- CLOSE: " + Close);
      double Open =iOpen(NULL,_Period,1);
      double Close = iClose(NULL,_Period,1);
       
      double ma[];
      ArraySetAsSeries(ma,1);
      CopyBuffer(maHandle_SMA_8,MAIN_LINE,0,3,ma);
      
  
    
      
      if(
         Open < ma[0] && Close > ma[0]  
         
          ){
         Comment("BUY");
         
         //trade.PositionClose(_Symbol);
         trade.Buy(Lots,NULL,Ask,(Ask-50 * _Point),(Ask+7 * _Point),NULL);
      }else if(
         Open > ma[0] && Close < ma[0]  
      ){
         Comment("SELL");
         
         //trade.PositionClose(_Symbol);
         trade.Sell(Lots,NULL,Bid,(Bid+50 * _Point),(Bid-7 * _Point), NULL);
      }
      
      
      
//      if(ma[1] > ma [0] && maDirection <=0){
//         maDirection=1;
//      
//         trade.PositionClose(_Symbol);
//         trade.Buy(Lots,NULL,Ask,(Ask-50 * _Point),(Ask+7 * _Point),NULL);
//      }else if(ma [1] < ma[0] && maDirection >=0){
//         maDirection=-1;
//         trade.PositionClose(_Symbol);
//         trade.Sell(Lots,NULL,Bid,(Bid+50 * _Point),(Bid-7 * _Point), NULL);
//      }
      
      
   }


   
  }
//+------------------------------------------------------------------+
