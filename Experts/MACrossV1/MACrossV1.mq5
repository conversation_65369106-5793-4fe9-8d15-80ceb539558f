//+------------------------------------------------------------------+
//|                                                   ShortSys_v1.00 |
//|                                                      <PERSON> |
//|                                                                  |
//+------------------------------------------------------------------+
#property version   "1.00"
#property strict
#include <Trade\Trade.mqh>  // Include trade library for order handling

CTrade trade;  // MQL5 trade object for order execution

input int StartHour = 7;
input int TakeProfit = 30;
input int StopLoss = 30;
input double InitLots = 0.03;
input int MA_Period = 100;
double LotSize = 0; // Declare as a variable
static int ticket_01 = 0;
static int ticket_02 = 0;
input double max_lot_increase = 0.12;

double ma, ma4, ma8, ma14, ma18, ma50, ma100, ma200;
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
   // Initialization code here
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
   // Deinitialization code here
  }
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
   static bool IsFirstTickUp = false;
   static bool IsFirstTickDown = false;
   
   // Use iMA() function to retrieve moving averages
   ma    = iMA(_Symbol, _Period, MA_Period, 0, MODE_SMA, PRICE_CLOSE);
   ma4   = iMA(_Symbol, _Period, 14, 0, MODE_SMA, PRICE_CLOSE);
   ma8   = iMA(_Symbol, _Period, 14, 0, MODE_SMA, PRICE_CLOSE);
   ma14  = iMA(_Symbol, _Period, 14, 0, MODE_SMA, PRICE_CLOSE);
   ma18  = iMA(_Symbol, _Period, 14, 0, MODE_SMA, PRICE_CLOSE);
   ma50  = iMA(_Symbol, _Period, 50, 0, MODE_SMA, PRICE_CLOSE);
   ma100 = iMA(_Symbol, _Period, 100, 0, MODE_SMA, PRICE_CLOSE);
   ma200 = iMA(_Symbol, _Period, 200, 0, MODE_SMA, PRICE_CLOSE);
   MqlDateTime mdt;
TimeCurrent(mdt);
   
   if (mdt.hour >= StartHour)  // Replacing Hour() with TimeHour(TimeCurrent())
   {
      bool is_OrderOpen = false;

      if (PositionSelect(_Symbol)) // Select current position
      {
         is_OrderOpen = true;
      }
      
      if (!is_OrderOpen)
      {
         // Copy price data into arrays for High and Low values
         double highArray[], lowArray[];
         CopyHigh(_Symbol, _Period, 0, 150, highArray);  // Copy last 150 highs
         CopyLow(_Symbol, _Period, 0, 150, lowArray);    // Copy last 150 lows
         
         int PeakHigh = ArrayMaximum(highArray, 20); // Getting highest peak
         int PeakLow = ArrayMinimum(lowArray, 20);   // Getting lowest peak
         
         double LastHigh = iHigh(_Symbol, _Period, 1); // Use iHigh to get previous high
         double LastLow = iLow(_Symbol, _Period, 1);   // Use iLow to get previous low
         
         Comment("Highest: ", DoubleToString(highArray[PeakHigh], 2), 
                 "\n Lowest: ", DoubleToString(lowArray[PeakLow], 2),
                 "\n Diff: ", DoubleToString(
                       (
                       iClose(_Symbol, _Period, 1) - lowArray[PeakLow]
                       ) / 
                      NormalizeDouble ((Point * 10),1)
                       , 2)
                    
                 );
         
         if (ma4 > ma18 && ma8 > ma18 && ma50 > ma100 && ma50 > ma200 && iOpen(_Symbol, _Period, 0) > ma50)
         {
            if (!IsFirstTickUp)
            {
               IsFirstTickUp = true;
               IsFirstTickDown = false;
               
               double TP_ = SymbolInfoDouble(_Symbol, SYMBOL_BID) + TakeProfit * Point * 10;
               if (!trade.Buy(MM_Size(), _Symbol, SymbolInfoDouble(_Symbol, SYMBOL_ASK), SymbolInfoDouble(_Symbol, SYMBOL_BID) - StopLoss * Point * 10, TP_, "Long Order"))
               {
                  Alert("Error Sending Buy Order!");
               }
            }
         }
         
         if (ma4 < ma18 && ma8 < ma18 && ma50 < ma100 && ma50 < ma200 && iOpen(_Symbol, _Period, 0) < ma50)
         {
            if (!IsFirstTickDown)
            {
               IsFirstTickDown = true;
               IsFirstTickUp = false;
               
               double TP_ = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - TakeProfit * Point * 10;
               if (!trade.Sell(MM_Size(), _Symbol, SymbolInfoDouble(_Symbol, SYMBOL_BID), SymbolInfoDouble(_Symbol, SYMBOL_ASK) + StopLoss * Point * 10, TP_, "Short Order"))
               {
                  Alert("Error Sending Sell Order!");
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Money Management - Calculate position size                       |
//+------------------------------------------------------------------+
double MM_Size()
{
   double profit = 0;
   if (PositionSelect(_Symbol)) // Select the current position
   {
      profit = PositionGetDouble(POSITION_PROFIT);
      Print("Profit for the order: ", profit);
   }

   if (LotSize == 0)
   {
      LotSize = InitLots;  
   }
   else
   {
      if (profit > 1)
      {
         LotSize = MathMin(LotSize + 0.01, max_lot_increase);
      }
   }

   return LotSize;
}
