//+------------------------------------------------------------------+
//|                                                      MA_Cross.mq5|
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                       http://www.metaquotes.net/ |
//+------------------------------------------------------------------+
#include <Trade\Trade.mqh>

input int EMA8_Period = 8;
input int SMA16_Period = 16;
input int EMA75_Period = 75;
input int SMA200_Period = 200;
input int RSI_Period = 14;
input double RSI_Level = 50.0;
input double ATR_Period = 14;
input double BigCandleThreshold = 1.5;  // Multiples of ATR
input double Lots = 0.1;
input int StartHour = 6;  // 1 hour before London session (in GMT)
input int EndHour = 20;   // 1 hour before NY session ends (in GMT)


CTrade trade;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
  }
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
  {
   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);
   int currentHour = timeStruct.hour;

   if(currentHour < StartHour || currentHour > EndHour)
      return;

   double ema8 = iMA(NULL, PERIOD_CURRENT, EMA8_Period, 0, MODE_EMA, PRICE_CLOSE);
   double sma16 = iMA(NULL, PERIOD_CURRENT, SMA16_Period, 0, MODE_SMA, PRICE_CLOSE);
   double ema75 = iMA(NULL, PERIOD_CURRENT, EMA75_Period, 0, MODE_EMA, PRICE_CLOSE);
   double sma200 = iMA(NULL, PERIOD_CURRENT, SMA200_Period, 0, MODE_SMA, PRICE_CLOSE);
   double rsi = iRSI(NULL, PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);
   double atr = iATR(NULL, PERIOD_CURRENT, ATR_Period);

   double closePrice = iClose(NULL, 0, 0);
   double openPrice = iOpen(NULL, 0, 0);

   bool buySignal = (ema8 > sma16 && rsi > RSI_Level && closePrice > ema75 && closePrice > sma200);
   bool sellSignal = (ema8 < sma16 && rsi < RSI_Level && closePrice < ema75 && closePrice < sma200);

   bool bigCandle = MathAbs(closePrice - openPrice) >= BigCandleThreshold * atr;
   bool crossAllMAs = closePrice > ema8 && closePrice > sma16 && closePrice > ema75;

   bool newBuySignal = crossAllMAs && bigCandle;
   bool newSellSignal = crossAllMAs && bigCandle;



   double stopLoss = atr * 1.5;

   if(buySignal || newBuySignal)
     {
      trade.Buy(Lots, NULL, closePrice, closePrice - stopLoss, 0, "Buy Signal");
     }

   if(sellSignal || newSellSignal)
     {
      trade.Sell(Lots, NULL, closePrice, closePrice + stopLoss, 0, "Sell Signal");
     }

   double bbUpper, bbMiddle, bbLower;
   BollingerBands(closePrice, 20, 2.0, bbUpper, bbMiddle, bbLower);

   bool exitBuyCondition = closePrice < ema8 || closePrice < sma16 || closePrice >= bbUpper;
   bool exitSellCondition = closePrice > ema8 || closePrice > sma16 || closePrice <= bbLower;

   if(exitBuyCondition)
      trade.PositionClose(Symbol(), POSITION_TYPE_BUY);

   if(exitSellCondition)
      trade.PositionClose(Symbol(), POSITION_TYPE_SELL);
  }
//+------------------------------------------------------------------+
//| Calculate Bollinger Bands                                        |
//+------------------------------------------------------------------+
void BollingerBands(double price, int length, double stddev, double &upper, double &middle, double &lower)
  {
   int bars = Bars(NULL, 0);
   if(bars < length) return;

   double sum = 0.0;
   for(int i = 0; i < length; i++)
     {
      sum += iClose(NULL, 0, i);
     }
   middle = sum / length;

   double sumSq = 0.0;
   for(int i = 0; i < length; i++)
     {
      sumSq += MathPow(iClose(NULL, 0, i) - middle, 2);
     }
   double variance = sumSq / length;
   double stdDeviation = MathSqrt(variance);

   upper = middle + stdDeviation * stddev;
   lower = middle - stdDeviation * stddev;
  }
