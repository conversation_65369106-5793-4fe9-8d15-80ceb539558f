//+------------------------------------------------------------------+
//|                                                ConfluenceCHOCH.mq5|
//|                      Multi-MA + RSI + MACD + BB + CHOCH (<PERSON><PERSON>per) |
//|  Description: Expert Advisor implementing Strategy 6 as requested |
//|  Notes:                                                            |
//|   - Uses closed-bar signals (shift=1) to reduce repainting.        |
//|   - Optional chart drawings for entries/exits.                     |
//|   - ATR-based SL, fixed TP (pips), BE+1 pip at +3 pips.            |
//|   - Higher-timeframe EMA200 filter (H1/H4 selectable).             |
//|   - Simple CHOCH break-retest using swing highs/lows.              |
//+------------------------------------------------------------------+
#property copyright   "Generated by ChatGPT"
#property link        ""
#property version     "1.00"
#property strict

#include <Trade/Trade.mqh>
CTrade trade;

//==================== Inputs ====================//
input string   Inp_Symbol                = "";        // Trading symbol (blank = current)
input ENUM_TIMEFRAMES Inp_Timeframe      = PERIOD_M5; // Signal timeframe
input ENUM_TIMEFRAMES Inp_HTF            = PERIOD_H1; // Higher TF for EMA200 filter

// Moving Averages
input int      Inp_LWMA_Period           = 50;        // LWMA period (trend smoother)
input int      Inp_SMA1_Period           = 50;        // SMA 50
input int      Inp_SMA2_Period           = 100;       // SMA 100
input int      Inp_SMA3_Period           = 200;       // SMA 200
input int      Inp_EMA1_Period           = 50;        // EMA 50
input int      Inp_EMA2_Period           = 75;        // EMA 75 (sentiment)
input int      Inp_EMA3_Period           = 100;       // EMA 100
input int      Inp_EMA4_Period           = 200;       // EMA 200 (current TF)
input int      Inp_EMA5_Period           = 10;        // EMA 10
input int      Inp_EMA6_Period           = 20;        // EMA 20
input int      Inp_EMA7_Period           = 8;         // EMA 8 (scalp trigger)
input int      Inp_EMA8_Period           = 16;        // EMA 16 (scalp trigger)

// RSI / MACD / BB / ATR
input int      Inp_RSI_Period            = 14;
input int      Inp_MACD_FastEMA          = 12;
input int      Inp_MACD_SlowEMA          = 26;
input int      Inp_MACD_Signal           = 9;
input int      Inp_BB_Period             = 20;
input double   Inp_BB_Dev                = 2.0;
input int      Inp_ATR_Period            = 14;
input double   Inp_ATR_Multiplier        = 1.5;       // SL = max(ATR*mult, MinSL)

// Risk/TP/SL
input double   Inp_FixedLots             = 0.10;      // Fixed lot size
input bool     Inp_UseMoneyManagement    = false;     // If true, risk % is used
input double   Inp_Risk_Percent          = 1.0;       // Risk % of free margin
input int      Inp_TP_Pips               = 7;         // Take Profit (pips)
input int      Inp_MinSL_Pips            = 5;         // Minimum Stop Loss (pips)
input int      Inp_BE_Activate_Pips      = 3;         // Move SL to BE+1 after X pips
input int      Inp_BE_Offset_Pips        = 1;         // BE offset (pips)
input int      Inp_Slippage_Points       = 20;        // Max deviation (points)

// CHOCH / Price Action
input int      Inp_Swing_Left            = 2;         // Swing left bars
input int      Inp_Swing_Right           = 2;         // Swing right bars
input int      Inp_CHOCH_Lookback        = 30;        // Lookback bars for break
input int      Inp_Retest_Lookback       = 10;        // Bars after break to find retest
input double   Inp_Retest_Tolerance_Pips = 2.0;       // Max distance from level for retest

// Bollinger context
input int      Inp_BB_Touch_Lookback     = 30;        // Lookback bars for prior BB touch
input double   Inp_BB_Middle_Tolerance_Pips = 1.5;    // Proximity to middle band

// Session / Limits / Filters
input int      Inp_MaxTradesPerDay       = 3;         // Per symbol per day
input bool     Inp_OnlyOnePosition       = true;      // Allow only one open position per symbol
input bool     Inp_ManualPauseTrading    = false;     // Manually pause around news/events

// Visualization
input bool     Inp_DrawSignals           = true;      // Draw arrows/labels on chart
input color    Inp_BuyColor              = clrLime;
input color    Inp_SellColor             = clrTomato;

// Misc
input ulong    Inp_Magic                 = 660006;    // Magic number

//==================== Globals ====================//
string         g_symbol;
int            g_digits;
double         g_point;
int            g_pipPoints; // points per pip

// Indicator handles (signal TF)
int hLWMA, hSMA50, hSMA100, hSMA200;
int hEMA50, hEMA75, hEMA100, hEMA200;
int hEMA10, hEMA20, hEMA8, hEMA16;
int hRSI, hMACD, hBB, hATR;
// Higher TF EMA200
int hHTF_EMA200;

// Trade/day counter
int   g_tradesToday = 0;
int   g_lastDate    = 0;

// Utility: bars calculation timeframe
MqlRates rates[]; // signal TF rates cache (optional)

//==================== Helpers ====================//
int PointsPerPip()
{
   // For most FX: 5-digit/3-digit = 10 points per pip, else 1
   if((int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS) == 3 || (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS) == 5)
      return 10;
   return 1;
}

double PipToPoints(double pips){ return pips * g_pipPoints; }

bool IsNewDay()
{
   datetime t = TimeCurrent();
   MqlDateTime s; TimeToStruct(t, s);
   int today = (int)(s.year*10000 + s.mon*100 + s.day);
   if(today != g_lastDate)
   {
      g_lastDate = today;
      g_tradesToday = 0;
      return true;
   }
   return false;
}

bool CopyOne(int handle, int buffer, int shift, double &val)
{
   double tmp[]; ArraySetAsSeries(tmp, true);
   if(CopyBuffer(handle, buffer, shift, 2, tmp) < 0) return false;
   val = tmp[shift];
   return true;
}

bool CopyTwo(int handle, int buffer, int shift, double &curr, double &prev)
{
   double tmp[]; ArraySetAsSeries(tmp, true);
   if(CopyBuffer(handle, buffer, shift, 3, tmp) < 0) return false;
   curr = tmp[shift];
   prev = tmp[shift+1];
   return true;
}

bool LoadRates(int count=200)
{
   ArraySetAsSeries(rates, true);
   int got = CopyRates(g_symbol, Inp_Timeframe, 0, count, rates);
   return got>0;
}

//---------------- Swing High/Low detection ----------------//
bool IsSwingHigh(int index)
{
   for(int i=1; i<=Inp_Swing_Left; i++) if(rates[index].high <= rates[index+i].high) return false;
   for(int i=1; i<=Inp_Swing_Right; i++) if(rates[index].high <= rates[index-i].high) return false;
   return true;
}

bool IsSwingLow(int index)
{
   for(int i=1; i<=Inp_Swing_Left; i++) if(rates[index].low >= rates[index+i].low) return false;
   for(int i=1; i<=Inp_Swing_Right; i++) if(rates[index].low >= rates[index-i].low) return false;
   return true;
}

bool FindLastSwingHigh(int fromIndex, int toIndex, int &foundIndex, double &price)
{
   for(int i=fromIndex; i>=toIndex; --i)
   {
      if(IsSwingHigh(i)) { foundIndex=i; price=rates[i].high; return true; }
   }
   return false;
}

bool FindLastSwingLow(int fromIndex, int toIndex, int &foundIndex, double &price)
{
   for(int i=fromIndex; i>=toIndex; --i)
   {
      if(IsSwingLow(i)) { foundIndex=i; price=rates[i].low; return true; }
   }
   return false;
}

//---------------- BB context helpers ----------------//
bool PriorTouchedUpperBB(int lookback, int &touchIndex)
{
   for(int i=1; i<=lookback; ++i)
   {
      double tmpUp[], tmpMid[], tmpLow[]; ArraySetAsSeries(tmpUp,true); ArraySetAsSeries(tmpMid,true); ArraySetAsSeries(tmpLow,true);
      if(CopyBuffer(hBB, 0, i, 1, tmpUp)<0) return false;
      if(CopyBuffer(hBB, 1, i, 1, tmpMid)<0) return false;
      if(CopyBuffer(hBB, 2, i, 1, tmpLow)<0) return false;
      double bu=tmpUp[0];
      if(rates[i].high >= bu) { touchIndex = i; return true; }
   }
   return false;
}

bool PriorTouchedLowerBB(int lookback, int &touchIndex)
{
   for(int i=1; i<=lookback; ++i)
   {
      double tmpUp[], tmpMid[], tmpLow[]; ArraySetAsSeries(tmpUp,true); ArraySetAsSeries(tmpMid,true); ArraySetAsSeries(tmpLow,true);
      if(CopyBuffer(hBB, 0, i, 1, tmpUp)<0) return false;
      if(CopyBuffer(hBB, 1, i, 1, tmpMid)<0) return false;
      if(CopyBuffer(hBB, 2, i, 1, tmpLow)<0) return false;
      double bl=tmpLow[0];
      if(rates[i].low <= bl) { touchIndex = i; return true; }
   }
   return false;
}

bool NearMiddleBand(int shift, double tolPips)
{
   double mid[]; ArraySetAsSeries(mid,true);
   if(CopyBuffer(hBB, 1, shift, 1, mid)<0) return false;
   double midVal = mid[0];
   double lastClose = rates[shift].close;
   return (MathAbs(lastClose - midVal) <= PipToPoints(tolPips));
}

//---------------- HTF EMA200 trend filter ----------------//
bool HigherTFIsBullish()
{
   double ema[]; ArraySetAsSeries(ema,true);
   if(CopyBuffer(hHTF_EMA200, 0, 1, 1, ema)<0) return false;
   double price = iClose(g_symbol, Inp_HTF, 1);
   return price > ema[0];
}

bool HigherTFIsBearish()
{
   double ema[]; ArraySetAsSeries(ema,true);
   if(CopyBuffer(hHTF_EMA200, 0, 1, 1, ema)<0) return false;
   double price = iClose(g_symbol, Inp_HTF, 1);
   return price < ema[0];
}

//---------------- MA Confluence (signal TF) ----------------//
bool MAConfluenceBullish(int shift)
{
   double lwma, ema50, ema75, ema8, ema16;
   if(!CopyOne(hLWMA, 0, shift, lwma)) return false;
   if(!CopyOne(hEMA50, 0, shift, ema50)) return false;
   if(!CopyOne(hEMA75, 0, shift, ema75)) return false;
   if(!CopyOne(hEMA8, 0, shift, ema8)) return false;
   if(!CopyOne(hEMA16, 0, shift, ema16)) return false;
   double close = rates[shift].close;
   bool priceAbove = (close>lwma && close>ema50 && close>ema75);
   bool shortTerm = (ema8>ema16);
   // Secondary trend aligning: EMA10>EMA20>EMA50>EMA75
   double ema10, ema20; CopyOne(hEMA10,0,shift,ema10); CopyOne(hEMA20,0,shift,ema20);
   bool secondary = (ema10>ema20 && ema20>ema50 && ema50>ema75);
   return priceAbove && shortTerm && secondary;
}

bool MAConfluenceBearish(int shift)
{
   double lwma, ema50, ema75, ema8, ema16;
   if(!CopyOne(hLWMA, 0, shift, lwma)) return false;
   if(!CopyOne(hEMA50, 0, shift, ema50)) return false;
   if(!CopyOne(hEMA75, 0, shift, ema75)) return false;
   if(!CopyOne(hEMA8, 0, shift, ema8)) return false;
   if(!CopyOne(hEMA16, 0, shift, ema16)) return false;
   double close = rates[shift].close;
   bool priceBelow = (close<lwma && close<ema50 && close<ema75);
   bool shortTerm = (ema8<ema16);
   double ema10, ema20; CopyOne(hEMA10,0,shift,ema10); CopyOne(hEMA20,0,shift,ema20);
   bool secondary = (ema10<ema20 && ema20<ema50 && ema50<ema75);
   return priceBelow && shortTerm && secondary;
}

//---------------- RSI/MACD filters ----------------//
bool RSIOkay(int shift)
{
   double v; if(!CopyOne(hRSI,0,shift,v)) return false;
   return (v>=30.0 && v<=70.0);
}

bool MACDBullishIncreasing(int shift)
{
   double macdCurr, macdPrev, sigCurr, sigPrev, histCurr, histPrev;
   double tmp0[], tmp1[], tmp2[]; ArraySetAsSeries(tmp0,true); ArraySetAsSeries(tmp1,true); ArraySetAsSeries(tmp2,true);
   if(CopyBuffer(hMACD,0,shift,2,tmp0)<0) return false; // MACD
   if(CopyBuffer(hMACD,1,shift,2,tmp1)<0) return false; // SIGNAL
   if(CopyBuffer(hMACD,2,shift,2,tmp2)<0) return false; // HIST
   macdCurr=tmp0[0]; macdPrev=tmp0[1];
   sigCurr =tmp1[0]; sigPrev =tmp1[1];
   histCurr=tmp2[0]; histPrev=tmp2[1];
   return (macdCurr>sigCurr && histCurr>histPrev);
}

bool MACDBearishDecreasing(int shift)
{
   double tmp0[], tmp1[], tmp2[]; ArraySetAsSeries(tmp0,true); ArraySetAsSeries(tmp1,true); ArraySetAsSeries(tmp2,true);
   if(CopyBuffer(hMACD,0,shift,2,tmp0)<0) return false; // MACD
   if(CopyBuffer(hMACD,1,shift,2,tmp1)<0) return false; // SIGNAL
   if(CopyBuffer(hMACD,2,shift,2,tmp2)<0) return false; // HIST
   double macdCurr=tmp0[0], sigCurr=tmp1[0], histCurr=tmp2[0], histPrev=tmp2[1];
   return (macdCurr<sigCurr && histCurr<histPrev);
}

//---------------- CHOCH break-retest ----------------//
bool CHOCH_BreakRetest_Long(int shift)
{
   int from = shift + Inp_Swing_Right + 1;
   int to   = shift + Inp_Swing_Right + Inp_CHOCH_Lookback;
   int swingIdx; double swingPrice;
   if(!FindLastSwingHigh(from, to, swingIdx, swingPrice)) return false;
   // Break: a bar closed above the swing high after swingIdx
   bool broken=false; int breakIndex=-1;
   for(int i=swingIdx-1; i>=shift+1; --i)
   {
      if(rates[i].close > swingPrice)
      { broken=true; breakIndex=i; break; }
   }
   if(!broken) return false;
   // Retest within Retest_Lookback: low touches near swing price and closes above
   double tol = PipToPoints(Inp_Retest_Tolerance_Pips);
   for(int i=breakIndex-1; i>=MathMax(shift+1, breakIndex-Inp_Retest_Lookback); --i)
   {
      if(rates[i].low <= swingPrice+tol && rates[i].close > swingPrice)
         return true;
   }
   return false;
}

bool CHOCH_BreakRetest_Short(int shift)
{
   int from = shift + Inp_Swing_Right + 1;
   int to   = shift + Inp_Swing_Right + Inp_CHOCH_Lookback;
   int swingIdx; double swingPrice;
   if(!FindLastSwingLow(from, to, swingIdx, swingPrice)) return false;
   // Break: a bar closed below the swing low after swingIdx
   bool broken=false; int breakIndex=-1;
   for(int i=swingIdx-1; i>=shift+1; --i)
   {
      if(rates[i].close < swingPrice)
      { broken=true; breakIndex=i; break; }
   }
   if(!broken) return false;
   // Retest within Retest_Lookback: high touches near swing price and closes below
   double tol = PipToPoints(Inp_Retest_Tolerance_Pips);
   for(int i=breakIndex-1; i>=MathMax(shift+1, breakIndex-Inp_Retest_Lookback); --i)
   {
      if(rates[i].high >= swingPrice-tol && rates[i].close < swingPrice)
         return true;
   }
   return false;
}

//---------------- Order/Position helpers ----------------//
int CountOpenPositions()
{
   int total=0;
   for(int i=0;i<PositionsTotal();++i)
   {
      ulong ticket = PositionGetTicket(i);
      if(!PositionSelectByTicket(ticket)) continue;
      string sym = PositionGetString(POSITION_SYMBOL);
      long magic  = PositionGetInteger(POSITION_MAGIC);
      if(sym==g_symbol && (ulong)magic==(ulong)Inp_Magic) total++;
   }
   return total;
}

bool WithinTradeLimit()
{
   if(IsNewDay()){}
   if(g_tradesToday >= Inp_MaxTradesPerDay) return false;
   if(Inp_OnlyOnePosition && CountOpenPositions()>0) return false;
   return true;
}

void IncrementTradesToday(){ g_tradesToday++; }

// Lot sizing (optional risk %)
double CalcLots(double stopPoints)
{
   if(!Inp_UseMoneyManagement) return Inp_FixedLots;
   double riskMoney = AccountInfoDouble(ACCOUNT_MARGIN_FREE) * (Inp_Risk_Percent/100.0);
   double tickValue = SymbolInfoDouble(g_symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize  = SymbolInfoDouble(g_symbol, SYMBOL_TRADE_TICK_SIZE);
   if(tickSize<=0.0) tickSize=_Point;
   double stopPrice = stopPoints * _Point;
   if(stopPrice<=0.0) return Inp_FixedLots;
   double lot = riskMoney / ( (stopPrice/tickSize) * tickValue );
   double minLot = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_MAX);
   double lotStep= SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_STEP);
   lot = MathMax(minLot, MathMin(maxLot, lot));
   // Normalize to step
   lot = MathFloor(lot/lotStep)*lotStep;
   return lot;
}

// Manage BE move
void ManageBreakeven()
{
   for(int i=0;i<PositionsTotal();++i)
   {
      ulong ticket = PositionGetTicket(i);
      if(!PositionSelectByTicket(ticket)) continue;
      if(PositionGetString(POSITION_SYMBOL)!=g_symbol) continue;
      if(PositionGetInteger(POSITION_MAGIC)!=(long)Inp_Magic) continue;

      long type = PositionGetInteger(POSITION_TYPE);
      double priceOpen = PositionGetDouble(POSITION_PRICE_OPEN);
      double sl        = PositionGetDouble(POSITION_SL);
      double bid = SymbolInfoDouble(g_symbol, SYMBOL_BID);
      double ask = SymbolInfoDouble(g_symbol, SYMBOL_ASK);

      double beTrigger = PipToPoints(Inp_BE_Activate_Pips) * _Point;
      double beOffset  = PipToPoints(Inp_BE_Offset_Pips)  * _Point;

      if(type==POSITION_TYPE_BUY)
      {
         if(bid - priceOpen >= beTrigger)
         {
            double newSL = priceOpen + beOffset;
            if(sl < newSL)
            {
               trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP));
               if(Inp_DrawSignals) ObjectCreate(0, StringFormat("BE_%I64u", ticket), OBJ_ARROW_BUY, 0, TimeCurrent(), newSL);
            }
         }
      }
      else if(type==POSITION_TYPE_SELL)
      {
         if(priceOpen - ask >= beTrigger)
         {
            double newSL = priceOpen - beOffset;
            if(sl == 0 || sl > newSL)
            {
               trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP));
               if(Inp_DrawSignals) ObjectCreate(0, StringFormat("BE_%I64u", ticket), OBJ_ARROW_SELL, 0, TimeCurrent(), newSL);
            }
         }
      }
   }
}

// Draw signal helpers
void DrawSignal(string id, bool isBuy, datetime t, double price, string note)
{
   if(!Inp_DrawSignals) return;
   string name = StringFormat("%s_%s_%s", id, isBuy?"BUY":"SELL", IntegerToString((int)(t)));
   ENUM_OBJECT type = isBuy ? OBJ_ARROW_BUY : OBJ_ARROW_SELL;
   if(ObjectFind(0, name)>=0) ObjectDelete(0, name);
   ObjectCreate(0, name, type, 0, t, price);
   ObjectSetInteger(0, name, OBJPROP_COLOR, isBuy?Inp_BuyColor:Inp_SellColor);
   ObjectSetInteger(0, name, OBJPROP_WIDTH, 2);
   // Label
   string lname = name+"_LBL";
   if(ObjectFind(0,lname)>=0) ObjectDelete(0,lname);
   ObjectCreate(0, lname, OBJ_TEXT, 0, t, price);
   ObjectSetString(0, lname, OBJPROP_TEXT, note);
   ObjectSetInteger(0,lname, OBJPROP_COLOR, isBuy?Inp_BuyColor:Inp_SellColor);
}

//==================== Life-cycle ====================//
int OnInit()
{
   g_symbol  = (Inp_Symbol==""? _Symbol : Inp_Symbol);
   g_point   = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   g_digits  = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);
   g_pipPoints = PointsPerPip();

   // Indicators (signal TF)
   hLWMA   = iMA(g_symbol, Inp_Timeframe, Inp_LWMA_Period, 0, MODE_LWMA, PRICE_CLOSE);
   hSMA50  = iMA(g_symbol, Inp_Timeframe, Inp_SMA1_Period, 0, MODE_SMA, PRICE_CLOSE);
   hSMA100 = iMA(g_symbol, Inp_Timeframe, Inp_SMA2_Period, 0, MODE_SMA, PRICE_CLOSE);
   hSMA200 = iMA(g_symbol, Inp_Timeframe, Inp_SMA3_Period, 0, MODE_SMA, PRICE_CLOSE);
   hEMA50  = iMA(g_symbol, Inp_Timeframe, Inp_EMA1_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA75  = iMA(g_symbol, Inp_Timeframe, Inp_EMA2_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA100 = iMA(g_symbol, Inp_Timeframe, Inp_EMA3_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA200 = iMA(g_symbol, Inp_Timeframe, Inp_EMA4_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA10  = iMA(g_symbol, Inp_Timeframe, Inp_EMA5_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA20  = iMA(g_symbol, Inp_Timeframe, Inp_EMA6_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA8   = iMA(g_symbol, Inp_Timeframe, Inp_EMA7_Period, 0, MODE_EMA, PRICE_CLOSE);
   hEMA16  = iMA(g_symbol, Inp_Timeframe, Inp_EMA8_Period, 0, MODE_EMA, PRICE_CLOSE);

   hRSI    = iRSI(g_symbol, Inp_Timeframe, Inp_RSI_Period, PRICE_CLOSE);
   hMACD   = iMACD(g_symbol, Inp_Timeframe, Inp_MACD_FastEMA, Inp_MACD_SlowEMA, Inp_MACD_Signal, PRICE_CLOSE);
   hBB     = iBands(g_symbol, Inp_Timeframe, Inp_BB_Period, Inp_BB_Dev, 0, PRICE_CLOSE);
   hATR    = iATR(g_symbol, Inp_Timeframe, Inp_ATR_Period);

   // HTF EMA200
   hHTF_EMA200 = iMA(g_symbol, Inp_HTF, 200, 0, MODE_EMA, PRICE_CLOSE);

   if(!LoadRates()) return(INIT_FAILED);

   trade.SetExpertMagicNumber(Inp_Magic);
   Print("ConfluenceCHOCH initialized on ", g_symbol, " TF:", EnumToString(Inp_Timeframe));
   return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason)
{
   // Clean up objects if drawing enabled
   if(Inp_DrawSignals)
   {
      long total = ObjectsTotal(0, 0, -1);
      for(int i=total-1; i>=0; --i)
      {
         string name = ObjectName(0, i);
         if(StringFind(name, "BUY_")>=0 || StringFind(name, "SELL_")>=0 || StringFind(name, "BE_")>=0)
            ObjectDelete(0, name);
      }
   }
   Print("ConfluenceCHOCH deinitialized. Reason=", reason);
}

void OnTick()
{
   if(Inp_ManualPauseTrading) { ManageBreakeven(); return; }
   if(!LoadRates()) return;

   // Only act on new closed bar
   static datetime lastBarTime=0;
   if(rates[0].time == lastBarTime) { ManageBreakeven(); return; }

   // Evaluate on the just-closed bar (shift=1)
   int shift = 1;

   // Higher TF filter
   bool bullHTF = HigherTFIsBullish();
   bool bearHTF = HigherTFIsBearish();

   // Common filters
   bool rsiOk   = RSIOkay(shift);
   bool macdUp  = MACDBullishIncreasing(shift);
   bool macdDn  = MACDBearishDecreasing(shift);

   // Bollinger contexts
   int touchIdx;
   bool touchedUp   = PriorTouchedUpperBB(Inp_BB_Touch_Lookback, touchIdx);
   bool touchedDown = PriorTouchedLowerBB(Inp_BB_Touch_Lookback, touchIdx);
   bool nearMid     = NearMiddleBand(shift, Inp_BB_Middle_Tolerance_Pips);

   // ATR for SL sizing
   double atr; if(!CopyOne(hATR,0,shift,atr)) { ManageBreakeven(); return; }
   double minSLpoints = PipToPoints(Inp_MinSL_Pips);
   double atrSLpoints = (atr/_Point) * Inp_ATR_Multiplier;
   double stopPoints  = MathMax(minSLpoints, atrSLpoints);

   // Entry logic
   bool longOk=false, shortOk=false;

   if(bullHTF && MAConfluenceBullish(shift) && rsiOk && macdUp && touchedUp && nearMid && CHOCH_BreakRetest_Long(shift))
      longOk = true;
   if(bearHTF && MAConfluenceBearish(shift) && rsiOk && macdDn && touchedDown && nearMid && CHOCH_BreakRetest_Short(shift))
      shortOk = true;

   // Trade limits
   if(!WithinTradeLimit()) { lastBarTime = rates[0].time; ManageBreakeven(); return; }

   double tpPoints = PipToPoints(Inp_TP_Pips);
   double ask = SymbolInfoDouble(g_symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(g_symbol, SYMBOL_BID);

   if(longOk)
   {
      double sl = bid - stopPoints*_Point;
      double tp = bid + tpPoints*_Point;
      double lots = CalcLots(stopPoints);
      trade.SetDeviationInPoints(Inp_Slippage_Points);
      if(trade.Buy(lots, g_symbol, ask, sl, tp, "CHOCH_Long"))
      {
         IncrementTradesToday();
         if(Inp_DrawSignals) DrawSignal("ENTRY", true, rates[shift].time, bid, "LONG");
      }
   }
   else if(shortOk)
   {
      double sl = ask + stopPoints*_Point;
      double tp = ask - tpPoints*_Point;
      double lots = CalcLots(stopPoints);
      trade.SetDeviationInPoints(Inp_Slippage_Points);
      if(trade.Sell(lots, g_symbol, bid, sl, tp, "CHOCH_Short"))
      {
         IncrementTradesToday();
         if(Inp_DrawSignals) DrawSignal("ENTRY", false, rates[shift].time, ask, "SHORT");
      }
   }

   lastBarTime = rates[0].time;
   ManageBreakeven();
}

//+------------------------------------------------------------------+
//|                 END OF FILE                                      |
//+------------------------------------------------------------------+
