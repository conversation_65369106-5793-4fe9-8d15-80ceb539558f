#include <Trade/Trade.mqh>

//+------------------------------------------------------------------+
//|                                                           8P.mq5 |
//|                                  Copyright 2022, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+


input double Lots =0.03;
input double TP = 100;
input double SL = 200;
input int trend = 0;

int maHandle_SMA_8;
int maHandle_LWMA_50;
int maHandle_EMA_100;
int maHandle_EMA_200;
int barsTotal;
int maDirection;

int m8cross=0;
int maAligned=0;

CTrade trade;

int OnInit(){

   maHandle_SMA_8= iMA(_Symbol,_Period,8,0,MODE_SMA,PRICE_CLOSE);
   maHandle_LWMA_50= iMA(_Symbol,_Period,50,0,MODE_LWMA,PRICE_CLOSE);
   maHandle_EMA_100= iMA(_Symbol,_Period,100,0,MODE_EMA,PRICE_CLOSE);
   maHandle_EMA_200= iMA(_Symbol,_Period,200,0,MODE_EMA,PRICE_CLOSE);
   barsTotal = iBars(_Symbol,_Period);
           
   
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {
//---
   
  }
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick(){


      double Ask=SymbolInfoDouble(_Symbol,SYMBOL_ASK);
      double Bid=SymbolInfoDouble(_Symbol,SYMBOL_BID);

    
      //ObjectCreate(0,"lable11",OBJ_LABEL,0,0,0);
      //ObjectSetString(0,"lable11",OBJPROP_FONT,"Arial");
      //ObjectSetInteger(0,"lable11",OBJPROP_FONTSIZE,12);
      //ObjectSetString(0,"lable11",OBJPROP_TEXT,"ASK : "+ opne + " -- Bid: "+ cloe);
      //ObjectSetInteger(0,"lable11",OBJPROP_XDISTANCE, 20);
      //ObjectSetInteger(0,"lable11",OBJPROP_YDISTANCE, 20);
      
     
      
      //Comment("orders: "+PositionsTotal());
      
   int bars= iBars(_Symbol,_Period);
   if(barsTotal < bars){
      barsTotal = bars;
      
      //double Open =NormalizeDouble(iOpen(NULL,_Period,1),_Digits);
      //double Close = NormalizeDouble(iClose(NULL,_Period,1),_Digits);
      
//      Comment("OPE: "+Open+" -- CLOSE: " + Close);
      double Open =iOpen(NULL,_Period,1);
      double Close = iClose(NULL,_Period,1);
       
      double ma[],ma50[],ma200[],ma100[];
      
      ArraySetAsSeries(ma,1);
      CopyBuffer(maHandle_SMA_8,MAIN_LINE,0,3,ma);

      ArraySetAsSeries(ma50,1);
      CopyBuffer(maHandle_LWMA_50,MAIN_LINE,0,3,ma50);      
        
      ArraySetAsSeries(ma100,1);
      CopyBuffer(maHandle_EMA_100,MAIN_LINE,0,3,ma100);  
      
      ArraySetAsSeries(ma200,1);
      CopyBuffer(maHandle_EMA_200,MAIN_LINE,0,3,ma200);      
  
  
      
       
      if( Open < ma[1] && Close > ma[1]   ){
         m8cross=1;
      }else if(   Open > ma[1] && Close < ma[1]){
         m8cross=2;
      }
    
      if(  
             ma100[0] > ma200[0]                
         &&  ma50[0] > ma100[0]
         &&  ma[0] > ma50[0]    
         && (ma100[0] - ma200[0]) > 0.00007
         && (ma50[0] - ma100[0] ) > 0.00007     
       ){
         maAligned=1;
       }else if(
          ma100[0] < ma200[0]
         && ma50[0] < ma100[0]
         &&  ma[0] < ma50[0]  
         && (ma200[0] - ma100[0]) > 0.00007
         && (ma100[0] - ma50[0] ) > 0.00007
       ){
        maAligned=2;
       }
       
       
       Comment("maAligned: "+maAligned +"  || m8cross: "+m8cross + " ma200: "+ma200[0]+ " ma100: "+ma100[0]+ " ma50: "+ma50[0]);
       
       if(PositionsTotal()<1){
       if(
           m8cross==1 
           && maAligned==1
           && Close > ma[1]
           //&& Close > ma50[0]
           && Close > ma200[0]
           && Open < Close
         ){
            //buy
            if(trend==1 ){
               trade.Buy(Lots,NULL,Ask,(Ask-SL * _Point),(Ask+TP * _Point),NULL);
            }
         }else if(
           m8cross==2 
           && maAligned==2
           &&  Close < ma[1]
           //&& Close < ma50[0]
           && Close < ma200[0]
           && Open > Close
         ){
            //sell
            if(trend==2){
            trade.Sell(Lots,NULL,Bid,(Bid+SL * _Point),(Bid-TP * _Point), NULL);
            }
         }
       
       }
//    if(PositionsTotal()<1){
//      
//      if(
//         ( Open < ma[1] && Close > ma[1]   )
//         && Close > ma50[0]
//         && Close > ma200[0]
//         &&  ma50[0] > ma200[0]
//         &&  ma[0] > ma50[0]
//         
//          ){
//         Comment("BUY");
//         
//         //trade.PositionClose(_Symbol);
//         trade.Buy(Lots,NULL,Ask,(Ask-SL * _Point),(Ask+TP * _Point),NULL);
//      }else if(
//         ( Open > ma[1] && Close < ma[1]    )
//         && Close < ma50[0]
//         && Close < ma200[0]
//         &&  ma50[0] < ma200[0]
//         &&  ma[0] < ma50[0]
//      ){
//         Comment("SELL");
//         
//         //trade.PositionClose(_Symbol);
//         trade.Sell(Lots,NULL,Bid,(Bid+SL * _Point),(Bid-TP * _Point), NULL);
//      }
//      
//      }
//      
//      if(ma[1] > ma [0] && maDirection <=0){
//         maDirection=1;
//      
//         trade.PositionClose(_Symbol);
//         trade.Buy(Lots,NULL,Ask,(Ask-50 * _Point),(Ask+7 * _Point),NULL);
//      }else if(ma [1] < ma[0] && maDirection >=0){
//         maDirection=-1;
//         trade.PositionClose(_Symbol);
//         trade.Sell(Lots,NULL,Bid,(Bid+50 * _Point),(Bid-7 * _Point), NULL);
//      }
      
      
   }


   
  }
//+------------------------------------------------------------------+
