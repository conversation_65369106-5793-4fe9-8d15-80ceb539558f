
//+------------------------------------------------------------------+
//|                                                   ShortSys_v1.00 |
//|                                                      <PERSON> |
//|                                                                  |
//+------------------------------------------------------------------+
#property version   "1.00"
#property strict
#include <Trade\Trade.mqh>
#include <TradeLib1.mqh>




CTrade trade;

input ENUM_TIMEFRAMES direction_timeframe = PERIOD_H1;
input ENUM_TIMEFRAMES executoi_timeframe = PERIOD_M15;

input ENUM_TIMEFRAMES SL_candlePeriod = PERIOD_M5;
input int SL_candleCount = 10;
//input double SL_candleCount = 5;
input int TP_points = 70;
input double lot_size=0.01;
input double rsi_threshold=15;
input int trade_only=2;

input int sl_ma=50;
// Conditional Functions
// Input variable for stop-loss timeframe
input ENUM_TIMEFRAMES StopLossTimeframe = PERIOD_M2;

// ... (Your existing functions: Check_moving_avg_close, Check_bb_mid_position, Check_bb_bands_toch, IsPositionOpen, Get_last_n_candlers_hl)

// Global variable to store the object name
string g_panelObjectName = "MyInfoPanel";

// Function to create/update the info panel
void UpdateInfoPanel(const string text) {
    if (ObjectFind(0, g_panelObjectName) == -1) {
        if (!ObjectCreate(0, g_panelObjectName, OBJ_LABEL, 0, 0, 0)) {
            Print("Error creating object: ", GetLastError());
            return;
        }
        ObjectSetInteger(0, g_panelObjectName, OBJPROP_CORNER, CORNER_LEFT_LOWER);
        ObjectSetInteger(0, g_panelObjectName, OBJPROP_XDISTANCE, 20);
        ObjectSetInteger(0, g_panelObjectName, OBJPROP_YDISTANCE, 50);
        ObjectSetInteger(0, g_panelObjectName, OBJPROP_COLOR, clrBlue);
        ObjectSetInteger(0, g_panelObjectName, OBJPROP_FONTSIZE, 12);
        ObjectSetInteger(0, g_panelObjectName, OBJPROP_BGCOLOR, clrBlack); // Corrected: OBJPROP_BGCOLOR
        ObjectSetInteger(0, g_panelObjectName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    }

    ObjectSetString(0, g_panelObjectName, OBJPROP_TEXT, text);
}



// Example usage in OnTick()
//void OnTick() {
//
//    int ma_check = Check_moving_avg_close(PERIOD_H1, MODE_EMA, PRICE_CLOSE, 20);
//    int bb_mid_check = Check_bb_mid_position(PERIOD_H1, 20, 2);
//    int bb_touch_check = Check_bb_bands_toch(PERIOD_H1, 20, 2);
//    int rsi_check = Check_rsi_levl(PERIOD_H1, 50);
//    double last_3_high = Get_last_n_candlers_hl(PERIOD_H1, 3, true);
//
//    if(ma_check == 1 && bb_mid_check == 1 && bb_touch_check == 1 && rsi_check == 1)
//    {
//        //Open buy position
//        trade.Buy(0.01, Symbol());
//    }
//    if(ma_check == 2 && bb_mid_check == 2 && bb_touch_check == 2 && rsi_check == 2)
//    {
//        //Open sell position
//        trade.Sell(0.01, Symbol());
//    }
//
//    Print("MA Check: ", ma_check, ", BB Mid Check: ", bb_mid_check, ", BB Touch Check: ", bb_touch_check, ", RSI Check: ", rsi_check, ", Last 3 High: ", last_3_high);
//}

bool IsPositionOpen() {
  for (int i = PositionsTotal() - 1; i >= 0; i--) {
    if (PositionGetSymbol(i) == Symbol()) {
      return true;
    }
  }
  return false;
}


void closeOpenPositions_8_16(){

// Check and modify/close open positions
    for (int i = PositionsTotal() - 1; i >= 0; i--) {
        if (PositionGetSymbol(i) == Symbol()) {
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentPrice = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? SymbolInfoDouble(Symbol(), SYMBOL_BID) : SymbolInfoDouble(Symbol(), SYMBOL_ASK);
            double lwma8 = get_ma_val(executoi_timeframe,MODE_LWMA,8);                          
            double sma16 = get_ma_val(executoi_timeframe,MODE_SMA,16);
            double close = iClose(Symbol(), executoi_timeframe, 0);

            if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
                if (close < lwma8 && lwma8 < sma16 ) {
                    trade.PositionClose(Symbol()); // Close the buy position
                    // Draw Arrow
                   datetime time = TimeCurrent(); // Correct way to get current time
                   double price = currentPrice;
                   string obj_name = "BuyArrow_" + (string)time;
                   if(ObjectCreate(0,obj_name, OBJ_ARROW_UP, 0, time, price)) {
                       ObjectSetInteger(0, obj_name, OBJPROP_COLOR, clrGreen);
                       ObjectSetInteger(0, obj_name, OBJPROP_ARROWCODE, 241); // Up arrow
                       ObjectSetInteger(0, obj_name, OBJPROP_TIME, time);
                       ObjectSetDouble(0, obj_name, OBJPROP_PRICE, price);
                       ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 6); // Set arrow width (larger)
                   }
                }
            } else if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL) {
                if (close > lwma8 && lwma8 > sma16 ) {
                    trade.PositionClose(Symbol()); // Close the sell position
                    // Draw Arrow
                      datetime time = TimeCurrent(); // Correct way to get current time
                      double price = currentPrice;
                      string obj_name = "SellArrow_" + (string)time;
                         if(ObjectCreate(0,obj_name, OBJ_ARROW_DOWN, 0, time, price)) {
                          ObjectSetInteger(0, obj_name, OBJPROP_COLOR, clrRed);
                          ObjectSetInteger(0, obj_name, OBJPROP_ARROWCODE, 242); // Down arrow
                          ObjectSetInteger(0, obj_name, OBJPROP_TIME, time);
                          ObjectSetDouble(0, obj_name, OBJPROP_PRICE, price);
                          ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 6); // Set arrow width (larger)
                      }
                }
            }
        }
    }


}

void closeOpenPositions_50(ENUM_TIMEFRAMES timeframe){

// Check and modify/close open positions
    for (int i = PositionsTotal() - 1; i >= 0; i--) {
        if (PositionGetSymbol(i) == Symbol()) {
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double currentPrice = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? SymbolInfoDouble(Symbol(), SYMBOL_BID) : SymbolInfoDouble(Symbol(), SYMBOL_ASK);
            double lwma50 = get_ma_val(timeframe,MODE_LWMA,50);                          
               
             lwma50 = lwma50  +50 * Point();

            double close = iClose(Symbol(), timeframe, 0);

            if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) {
                if (close < lwma50 ) {
                    trade.PositionClose(Symbol()); // Close the buy position
                    // Draw Arrow
                   datetime time = TimeCurrent(); // Correct way to get current time
                   double price = currentPrice;
                   string obj_name = "BuyArrow_" + (string)time;
                   if(ObjectCreate(0,obj_name, OBJ_ARROW_UP, 0, time, price)) {
                       ObjectSetInteger(0, obj_name, OBJPROP_COLOR, clrGreen);
                       ObjectSetInteger(0, obj_name, OBJPROP_ARROWCODE, 241); // Up arrow
                       ObjectSetInteger(0, obj_name, OBJPROP_TIME, time);
                       ObjectSetDouble(0, obj_name, OBJPROP_PRICE, price);
                       ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 6); // Set arrow width (larger)
                   }
                }
            } else if (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL) {
                if (close > lwma50 ) {
                    trade.PositionClose(Symbol()); // Close the sell position
                    // Draw Arrow
                      datetime time = TimeCurrent(); // Correct way to get current time
                      double price = currentPrice;
                      string obj_name = "SellArrow_" + (string)time;
                         if(ObjectCreate(0,obj_name, OBJ_ARROW_DOWN, 0, time, price)) {
                          ObjectSetInteger(0, obj_name, OBJPROP_COLOR, clrRed);
                          ObjectSetInteger(0, obj_name, OBJPROP_ARROWCODE, 242); // Down arrow
                          ObjectSetInteger(0, obj_name, OBJPROP_TIME, time);
                          ObjectSetDouble(0, obj_name, OBJPROP_PRICE, price);
                          ObjectSetInteger(0, obj_name, OBJPROP_WIDTH, 6); // Set arrow width (larger)
                      }
                }
            }
        }
    }


}


//double VWAP()
//  {
//     double VWAP_Sum = 0.0;
//     double Volume_Sum = 0.0;
//     double Volume = iVolume(NULL, 0, 0); 
//     double Close = iClose(Symbol(), Period(), 0);
//
//
//     for(int i = 0; i < Bars - 1; i++) 
//      {
//          Volume_Sum += iVolume(NULL, i, 0); 
//          VWAP_Sum += Close[i] * iVolume(NULL, i, 0); 
//      }
//
//     if(Volume_Sum > 0) 
//         return VWAP_Sum / Volume_Sum;
//     else 
//         return 0.0; 
//  }

//+------------------------------------------------------------------+
//| Function to Calculate VWAP                                       |
//+------------------------------------------------------------------+
double CalculateVWAP(int period, int shift)
{
   // Check for valid shift
   if (shift < period - 1)
      return 0;

   double cumulativeTPV = 0.0;  // Cumulative Typical Price * Volume
   double cumulativeVolume = 0.0; // Cumulative Volume

   // Loop through the specified period
   for (int i = shift; i > shift - period && i >= 0; i--)
   {
      double typicalPrice = (iHigh(NULL, 0, i) + iLow(NULL, 0, i) + iClose(NULL, 0, i)) / 3.0;
      double volume = iVolume(NULL, 0, i);

      cumulativeTPV += typicalPrice * volume;
      cumulativeVolume += volume;
   }

   // Calculate VWAP
   return (cumulativeVolume > 0) ? cumulativeTPV / cumulativeVolume : 0.0;
}
  

void OnTick__() {

   //closeOpenPositions_8_16();



  
   // --- Buy Conditions ---
  bool condition_a_buy = false;
  bool condition_b_buy = false;
  
   double ma4  = get_ma_val(executoi_timeframe,MODE_LWMA,4);
   double ma8  = get_ma_val(executoi_timeframe,MODE_LWMA,8);
   double ma16  = get_ma_val(executoi_timeframe,MODE_SMA,16);
   //double ma18  = iMA(Symbol(), Period(), 18, 0, MODE_EMA,PRICE_CLOSE);
   //double ma26  = iMA(Symbol(), Period(), 26, 0, MODE_EMA,PRICE_CLOSE);
   double ma50  = get_ma_val(executoi_timeframe,MODE_EMA,50);
   double ma100 = get_ma_val(executoi_timeframe,MODE_SMA,100);
   double ma200 = get_ma_val(executoi_timeframe,MODE_SMA,200);
   
   double close_price = iClose(Symbol(), direction_timeframe, 0);
   
   double _15_ma8 = get_ma_val(executoi_timeframe,MODE_EMA,8);
   double _15_ma16 = get_ma_val(executoi_timeframe,MODE_SMA,16);
      
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    string panelText = "ma8: " + DoubleToString(_15_ma8,Digits()) + " \n\n " + "ma16: " + DoubleToString(_15_ma16,Digits()) + "ma8<16 ? : " + (ma8 < ma16)  ;
    UpdateInfoPanel(panelText); // Update the panel
 

  if (IsPositionOpen()) {
    return; // Don't open a new position if one is already open
  }
//   
//   if( ma8 > ma16 && ma8 > ma50 &&  ma50  > ma100 && ma50  > ma200 && close_price >ma50 ){
//      condition_a_buy = true;
//   }
//   if (Check_moving_avg_close(executoi_timeframe, MODE_LWMA, PRICE_CLOSE, 50) == 1 &&
//        Check_moving_avg_close(executoi_timeframe, MODE_LWMA, PRICE_CLOSE, 8) == 1 &&
//        Check_moving_avg_close(executoi_timeframe, MODE_EMA, PRICE_CLOSE, 75) == 1 &&
//        ma8 > ma16
//        ) { //Keep this for comparison
//    condition_b_buy = true;
//  }
//
//   
//   
//    if (condition_a_buy && condition_b_buy) {
//       double bid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
//       if(bid_price == EMPTY_VALUE) return; //Check for invalid price
//       int spread = (int)SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);
//       double stopLoss = Get_last_n_candlers_hl(SL_candlePeriod, SL_candleCount, false) - (spread + 5 * Point()); // Spread removed
//       double takeProfit = bid_price + 70 * Point();
//       if(stopLoss < bid_price){
//         //trade.Buy(0.01, Symbol(), 0, stopLoss, takeProfit);
//   
//       }
//     }
//  
   
   
    // --- Sell Conditions ---
  bool condition_a_sell = false;
  bool condition_b_sell = false;
         
   if(ma8 < ma16 && ma8 < ma50 && ma50  < ma100 && ma50  < ma200 && close_price < ma50 ){
      condition_a_sell = true;
   }

   // b) Execution Timeframe (2 min) Conditions
   if (Check_moving_avg_close(executoi_timeframe, MODE_LWMA, PRICE_CLOSE, 50) == 2 &&
        Check_moving_avg_close(executoi_timeframe, MODE_LWMA, PRICE_CLOSE, 8) == 2 &&
        Check_moving_avg_close(executoi_timeframe, MODE_EMA, PRICE_CLOSE, 75) == 2 &&
        ma8 < ma16) { //Keep this for comparison
    condition_b_sell = true;
  }


   // Open Sell Order
    if (condition_a_sell && condition_b_sell) {
        double ask_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        if(ask_price == EMPTY_VALUE) return; //Check for invalid price
    
       int spread = (int)SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);
       double stopLoss = MathMin(Get_last_n_candlers_hl(SL_candlePeriod, SL_candleCount, true) + (spread + 15 * Point()), ask_price +  10 * ( 10 * Point() ) ); // Spread removed
       double takeProfit = ask_price - 130 * Point();
        if(stopLoss > ask_price){
            trade.Sell(0.01, Symbol(), 0, stopLoss, takeProfit);
      
          
      }
  }



}

void OnTick() {

   //closeOpenPositions_8_16();

   closeOpenPositions_50(PERIOD_M5);

 
   double ma50  = get_ma_val(PERIOD_M5,MODE_LWMA,sl_ma);
   double rsiValue = GetRSI(Symbol(), PERIOD_M5, 30, PRICE_CLOSE, 0);
 
    string panelText = "rsiValue: " + DoubleToString(rsiValue,Digits()) ;
    UpdateInfoPanel(panelText); // Update the panel
   
   
  if (IsPositionOpen()) {
    return; // Don't open a new position if one is already open
  }

   // --- Buy Conditions ---
  bool condition_a_buy = false;
  bool condition_b_buy = false;

  // a) Higher Timeframe (15 min) Conditions
  //if (Check_moving_avg_close(PERIOD_M15, MODE_LWMA, PRICE_CLOSE, 50) == 1 &&
  //    Check_bb_mid_position(PERIOD_M15, 50, 3.0) == 1) {
  //  condition_a_buy = true;
  //}
    if (Check_moving_avg_close(PERIOD_M15, MODE_LWMA, PRICE_CLOSE, 200) == 1 && Check_moving_avg_close(PERIOD_M15, MODE_LWMA, PRICE_CLOSE, 50) == 1         
      ) {
    condition_a_buy = true;
  }
  // b) Execution Timeframe (2 min) Conditions
  //  if (Check_moving_avg_close(executoi_timeframe, MODE_LWMA, PRICE_CLOSE, 50) == 1 &&
  //      Check_moving_avg_close(executoi_timeframe, MODE_LWMA, PRICE_CLOSE, 8) == 1 &&
  //      Check_moving_avg_close(executoi_timeframe, MODE_EMA, PRICE_CLOSE, 75) == 1 &&
  //      iMA(Symbol(), executoi_timeframe, 8, 0, MODE_LWMA, PRICE_CLOSE) > iMA(Symbol(), executoi_timeframe, 16, 0, MODE_SMA, PRICE_CLOSE)) { //Keep this for comparison
  //  condition_b_buy = true;
  //}

   if (Check_moving_avg_close(PERIOD_M5, MODE_LWMA, PRICE_CLOSE, 50) == 1 && Check_moving_avg_close(PERIOD_M5, MODE_LWMA, PRICE_CLOSE, 200) == 1 &&
        Check_moving_avg_close(PERIOD_M5, MODE_LWMA, PRICE_CLOSE, 8) == 1  
        && rsiValue < rsi_threshold                 
        && get_ma_val(PERIOD_M5,MODE_LWMA,8) > get_ma_val(PERIOD_M5,MODE_SMA,16)       
        ) { //Keep this for comparison
       condition_b_buy = true;
     }
  // Open Buy Order
  if (condition_a_buy && condition_b_buy && trade_only==1) {
    double bid_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    if(bid_price == EMPTY_VALUE) return; //Check for invalid price
    int spread = (int)SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);
    //double stopLoss = Get_last_n_candlers_hl(PERIOD_M5, SL_candleCount, false) - (spread + 15 * Point()); // Spread removed
    //double takeProfit = bid_price + TP_points * Point();
    double stopLoss = ma50 - 70 * Point();
         double takeProfit = bid_price + TP_points * Point();
    if(stopLoss < bid_price){
      trade.Buy(lot_size, Symbol(), 0, stopLoss, takeProfit);
     
    }
  }
  
   // --- Sell Conditions ---
  bool condition_a_sell = false;
  bool condition_b_sell = false;

  // a) Higher Timeframe (15 min) Conditions
    if (Check_moving_avg_close(PERIOD_M15, MODE_LWMA, PRICE_CLOSE, 200) == 2 && Check_moving_avg_close(PERIOD_M15, MODE_LWMA, PRICE_CLOSE, 50) == 2 
         //&& Check_bb_mid_position(direction_timeframe, 50, 3.0) == 2
      ) {
    condition_a_sell = true;
  }

  // b) Execution Timeframe (2 min) Conditions
    if (Check_moving_avg_close(PERIOD_M5, MODE_LWMA, PRICE_CLOSE, 50) == 2 && Check_moving_avg_close(PERIOD_M5, MODE_LWMA, PRICE_CLOSE, 200) == 2 &&
        Check_moving_avg_close(PERIOD_M5, MODE_LWMA, PRICE_CLOSE, 8) == 2  
        && rsiValue > rsi_threshold 
        //Check_moving_avg_close(PERIOD_M5, MODE_EMA, PRICE_CLOSE, 75) == 2 &&
        
        && get_ma_val(PERIOD_M5,MODE_LWMA,8) < get_ma_val(PERIOD_M5,MODE_SMA,16)
        //iMA(Symbol(), PERIOD_M5, 8, 0, MODE_LWMA, PRICE_CLOSE) < iMA(Symbol(), PERIOD_M5, 16, 0, MODE_SMA, PRICE_CLOSE)
        
        ) { //Keep this for comparison
       condition_b_sell = true;
     }

  // Open Sell Order
    if (condition_a_sell && condition_b_sell && trade_only==2) {
        double ask_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        if(ask_price == EMPTY_VALUE) return; //Check for invalid price
    
       int spread = (int)SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);
       //double stopLoss = Get_last_n_candlers_hl(PERIOD_M5, 3, true) + (spread + 15 * Point()); // Spread removed
       double stopLoss = ma50 + 70 * Point();
       double takeProfit = ask_price - TP_points * Point();
        if(stopLoss > ask_price){
            trade.Sell(lot_size, Symbol(), 0, stopLoss, takeProfit);      
      }
  }
}



int OnInit()
{
    trade.SetDeviationInPoints(50); // Set slippage to 5 pips
    return(INIT_SUCCEEDED);
}


void OnDeinit(const int reason) {

    ObjectDelete(0, g_panelObjectName); // Delete the object on deinitialization

  // Delete all arrow objects
  for (int i = ObjectsTotal(0, OBJ_ARROW); i >= 0; i--) {
    string obj_name = ObjectName(0, i, OBJ_ARROW);
    if (StringFind(obj_name, "BuyArrow_") == 0 || StringFind(obj_name, "SellArrow_") == 0) {
      ObjectDelete(0, obj_name);
    }
  }
}
