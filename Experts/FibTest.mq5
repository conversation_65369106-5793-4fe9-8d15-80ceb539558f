// Define the input parameters for the Fibonacci retracement levels
input int Fibonacci_levels = 5; // Number of levels to draw
input int Fibonacci_start = 0;  // Start point for the levels (0 = automatic)
input int Fibonacci_end = 0;    // End point for the levels (0 = automatic)

// Define the global variables
int start, end;

// This function is called every time a new candle is formed on the chart
void OnTick()
{
    // Check if the Fibonacci start and end points are set to automatic
    if (Fibonacci_start == 0 || Fibonacci_end == 0)
    {
        // If so, use the highest and lowest prices from the last 20 candles as the start and end points
        start = iHigh(NULL, 0, MODE_HIGH, 20);
        end = iLow(NULL, 0, MODE_LOW, 20);
    }
    else
    {
        // If not, use the user-defined start and end points
        start = Fibonacci_start;
        end = Fibonacci_end;
    }

    // Calculate the Fibonacci retracement levels and draw them on the chart
    for (int i = 0; i < Fibonacci_levels; i++)
    {
        double level = start + (end - start) * fibonacci(i);
        ObjectCreate("Fibonacci level " + (i + 1), OBJ_HLINE, 0, Time[0], level, Time[20], level);
    }
}
