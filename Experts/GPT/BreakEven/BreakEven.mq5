//+------------------------------------------------------------------+
//|                                                      ClosePositionsOnProfit.mq5|
//|                        Generated by MetaEditor 5                 |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include <Trade\Trade.mqh>

input double ProfitThreshold = 10.0; // The profit threshold to close positions
input double RangeThreshold = 1.0; // The profit threshold range up and down 
input bool CurrentSymbolOnly = true; // apply on current symbol only
CTrade trade;

int OnInit()
  {
   EventSetTimer(1); // Set a timer to update the EA every second
   return(INIT_SUCCEEDED);
  }

void OnDeinit(const int reason)
  {
   EventKillTimer(); // Kill the timer when the EA is removed
  }

void OnTick()
  {
   CheckProfitAndClosePositions();
  }

void OnTimer()
  {
   CheckProfitAndClosePositions();
  }

void CheckProfitAndClosePositions()
  {
   int total_positions = 0;
   double total_profit = 0;

   for (int i = 0; i < PositionsTotal(); i++)
     {
      ulong ticket = PositionGetTicket(i);
      if (PositionSelectByTicket(ticket))
        {
        if(CurrentSymbolOnly){
         if (PositionGetString(POSITION_SYMBOL) == Symbol())
           {
            total_positions++;
            double profit = PositionGetDouble(POSITION_PROFIT);
            total_profit += profit;
           }
          }else{
            total_positions++;
            double profit = PositionGetDouble(POSITION_PROFIT);
            total_profit += profit;
          }
        }
     }

   if (total_profit == ProfitThreshold ||
      (total_profit > ProfitThreshold  && total_profit <= ProfitThreshold + RangeThreshold)  ||
      (total_profit < ProfitThreshold  && total_profit >= ProfitThreshold - RangeThreshold)
   )
     {
      for (int i = PositionsTotal() - 1; i >= 0; i--)
        {
         ulong ticket = PositionGetTicket(i);
         if (PositionSelectByTicket(ticket))
           {
              if(CurrentSymbolOnly){
               if (PositionGetString(POSITION_SYMBOL) == Symbol())
                 {
                  trade.PositionClose(ticket);
                 }
              }else{
              trade.PositionClose(ticket);
              }
           }
        }
     }
  }
